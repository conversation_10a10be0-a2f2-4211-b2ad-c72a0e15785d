#!/usr/bin/env python3
"""Test script to understand Python rosbags implementation and data format."""

import sys
from pathlib import Path
from rosbags.rosbag2 import Reader
from rosbags.serde import deserialize_cdr

def main():
    bag_path = Path("~/Downloads/V1_03_difficult").expanduser()
    
    if not bag_path.exists():
        print(f"Bag path does not exist: {bag_path}")
        return 1
    
    print(f"Testing Python rosbags with: {bag_path}")
    print("=" * 60)
    
    # Open the bag
    with Reader(bag_path) as reader:
        print(f"Bag duration: {reader.duration / 1e9:.2f} seconds")
        print(f"Total messages: {reader.message_count}")
        print(f"Start time: {reader.start_time}")
        print(f"End time: {reader.end_time}")
        print()
        
        # List all topics
        print("Topics in bag:")
        for connection in reader.connections:
            print(f"  - {connection.topic} ({connection.msgtype}): {connection.msgcount} messages")
        print()
        
        # Focus on /fcu/imu topic
        target_topic = "/fcu/imu"
        target_connections = [x for x in reader.connections if x.topic == target_topic]
        
        if not target_connections:
            print(f"Topic {target_topic} not found!")
            return 1
        
        target_conn = target_connections[0]
        print(f"Target topic: {target_topic}")
        print(f"  Type: {target_conn.msgtype}")
        print(f"  Messages: {target_conn.msgcount}")
        print(f"  Connection ID: {target_conn.id}")
        print()
        
        # Read first few messages
        print("Reading first 5 messages:")
        message_count = 0
        
        for connection, timestamp, rawdata in reader.messages(target_connections):
            if message_count >= 5:
                break
                
            print(f"Message {message_count + 1}:")
            print(f"  Connection ID: {connection.id}")
            print(f"  Topic: {connection.topic}")
            print(f"  Timestamp: {timestamp}")
            print(f"  Raw data size: {len(rawdata)} bytes")
            print(f"  Raw data (first 32 bytes): {rawdata[:32].hex()}")
            
            # Try to deserialize the message
            try:
                msg = deserialize_cdr(rawdata, connection.msgtype)
                print(f"  Deserialized message type: {type(msg)}")
                if hasattr(msg, 'header'):
                    print(f"  Header timestamp: {msg.header.stamp.sec}.{msg.header.stamp.nanosec:09d}")
                    print(f"  Header frame_id: '{msg.header.frame_id}'")
                if hasattr(msg, 'orientation'):
                    print(f"  Orientation: x={msg.orientation.x:.6f}, y={msg.orientation.y:.6f}, z={msg.orientation.z:.6f}, w={msg.orientation.w:.6f}")
                if hasattr(msg, 'linear_acceleration'):
                    print(f"  Linear acceleration: x={msg.linear_acceleration.x:.6f}, y={msg.linear_acceleration.y:.6f}, z={msg.linear_acceleration.z:.6f}")
                if hasattr(msg, 'angular_velocity'):
                    print(f"  Angular velocity: x={msg.angular_velocity.x:.6f}, y={msg.angular_velocity.y:.6f}, z={msg.angular_velocity.z:.6f}")
                if hasattr(msg, 'orientation_covariance'):
                    print(f"  Orientation covariance (first 3): {msg.orientation_covariance[:3]}")
                if hasattr(msg, 'angular_velocity_covariance'):
                    print(f"  Angular velocity covariance (first 3): {msg.angular_velocity_covariance[:3]}")
                if hasattr(msg, 'linear_acceleration_covariance'):
                    print(f"  Linear acceleration covariance (first 3): {msg.linear_acceleration_covariance[:3]}")
            except Exception as e:
                print(f"  Failed to deserialize: {e}")
            
            print()
            message_count += 1
        
        # Count total messages for this topic
        print("Counting all messages for /fcu/imu...")
        total_count = 0
        total_bytes = 0
        
        for connection, timestamp, rawdata in reader.messages(target_connections):
            total_count += 1
            total_bytes += len(rawdata)
            
            if total_count % 5000 == 0:
                print(f"  Processed {total_count} messages...")
        
        print(f"Total messages extracted: {total_count}")
        print(f"Total data size: {total_bytes} bytes ({total_bytes / 1024:.2f} KB)")
        print(f"Average message size: {total_bytes / total_count:.1f} bytes")

if __name__ == "__main__":
    sys.exit(main())
