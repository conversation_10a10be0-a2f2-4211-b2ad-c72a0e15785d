# Copyright 2016 - 2023 Ternaris
# SPDX-License-Identifier: Apache-2.0
"""Generated declinate CLI tests."""

# DO NOT EDIT THIS FILE MANUALLY

import inspect

from declinate.check import check_package
from declinate.cli import main


def test_cli_is_up_to_date() -> None:
    """Test if compiled cli is up to date."""
    res = check_package('rosbags.convert')
    assert not res, res


def test_cli() -> None:
    """Cli."""
    assert inspect.isfunction(main)
