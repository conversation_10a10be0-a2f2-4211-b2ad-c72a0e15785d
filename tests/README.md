# Comprehensive Test Suite for rosbag2-rs

This directory contains a comprehensive test suite that validates the Rust rosbag2-rs implementation against the Python rosbags library to ensure byte-for-byte identical results.

## Overview

The test suite provides:

1. **Cross-validation against Python reference implementation**
2. **Comprehensive coverage of all ROS2 message types**
3. **Persistent and repeatable test fixtures**
4. **Detailed error reporting and validation**

## Test Files

### Core Test Files

- `integration_tests.rs` - Main integration test suite with cross-validation
- `python_reference.py` - Python reference implementation for data extraction
- `README.md` - This documentation

### Test Data

The tests use the permanent test bag fixtures located at:
- `bags/test_bags/test_bag_sqlite3` - SQLite3 format test bag
- `bags/test_bags/test_bag_mcap` - MCAP format test bag

Each bag contains:
- **94 topics** covering all major ROS2 message types
- **188 messages** total (2 messages per topic)
- **6 message categories**: geometry_msgs (29), sensor_msgs (27), std_msgs (30), nav_msgs (5), stereo_msgs (1), tf2_msgs (2)

## Test Categories

### 1. Basic Functionality Tests

- `test_read_sqlite3_bag()` - Verifies SQLite3 bag can be opened and read
- `test_read_mcap_bag()` - Verifies MCAP bag can be opened and read (when feature enabled)
- `test_all_messages_readable()` - Ensures all 188 messages can be read without errors

### 2. Cross-Validation Tests

- `test_sqlite3_cross_validation()` - Compares Rust vs Python for SQLite3 format
- `test_mcap_cross_validation()` - Compares Rust vs Python for MCAP format

These tests:
- Extract data using both Rust and Python implementations
- Compare raw message data byte-for-byte
- Validate timestamps, topics, and metadata
- Ensure 100% identical results

### 3. Message Type Coverage Tests

- `test_comprehensive_message_type_coverage()` - Validates all 94 message types
- `test_specific_message_types()` - Tests common message types individually
- Covers all ROS2 message categories:
  - `geometry_msgs/*` - 3D geometry primitives, poses, transforms
  - `sensor_msgs/*` - Sensor data (images, point clouds, IMU, etc.)
  - `std_msgs/*` - Standard message types (strings, numbers, arrays)
  - `nav_msgs/*` - Navigation messages (maps, paths, odometry)
  - `stereo_msgs/*` - Stereo camera messages
  - `tf2_msgs/*` - Transform messages

### 4. Filtering and Query Tests

- `test_message_filtering_by_topic()` - Tests topic-based message filtering
- `test_message_filtering_by_timestamp()` - Tests time-based message filtering

### 5. Format Consistency Tests

- `test_bag_format_consistency()` - Ensures SQLite3 and MCAP contain identical data

## Running Tests

### Prerequisites

1. **Python Environment Setup**:
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install rosbags
   ```

2. **Rust Dependencies**:
   ```bash
   cargo build
   ```

### Running Individual Tests

```bash
# Run basic functionality tests
cargo test --test integration_tests test_read_sqlite3_bag

# Run cross-validation tests
cargo test --test integration_tests test_sqlite3_cross_validation

# Run all integration tests
cargo test --test integration_tests
```

### Running Comprehensive Validation

Use the provided script for a complete validation run:

```bash
./scripts/run_comprehensive_validation.sh
```

This script:
- Checks all prerequisites
- Runs the complete test suite
- Provides detailed validation results
- Generates a comprehensive summary report

## Cross-Validation Process

The cross-validation process works as follows:

1. **Python Reference Extraction**:
   - Uses `python_reference.py` to extract data from bag files
   - Leverages the established Python `rosbags` library
   - Outputs structured JSON with all message data

2. **Rust Implementation Extraction**:
   - Uses the Rust rosbag2-rs library to read the same bag files
   - Extracts identical data structure for comparison

3. **Validation**:
   - Compares message counts, topic counts, and metadata
   - Validates raw message data byte-for-byte
   - Checks timestamp accuracy
   - Reports any discrepancies with detailed error messages

## Expected Results

When all tests pass, you can be confident that:

- ✅ **Raw Data Identical**: Rust produces byte-for-byte identical message data
- ✅ **Timestamps Accurate**: All timestamps match exactly between implementations
- ✅ **Metadata Consistent**: Topic information and bag metadata are identical
- ✅ **Complete Coverage**: All 94 message types and 188 messages validated
- ✅ **Format Support**: Both SQLite3 and MCAP formats work identically

## Troubleshooting

### Common Issues

1. **Python rosbags not installed**:
   ```bash
   source venv/bin/activate
   pip install rosbags
   ```

2. **Test bag files missing**:
   - Ensure `bags/test_bags/` directory exists with both test bags
   - Run the bag generation script if needed

3. **MCAP tests skipped**:
   - Enable MCAP feature: `cargo test --features mcap`

4. **Cross-validation failures**:
   - Check that Python virtual environment is activated
   - Verify rosbags library version compatibility
   - Review detailed error output for specific mismatches

### Debugging Failed Tests

When cross-validation fails, the tests provide detailed output showing:
- Which topics have mismatched data
- Specific timestamp or data size differences
- Raw data comparison for debugging

Example error output:
```
Topic '/test/geometry_msgs/point': raw data mismatch at timestamp 1234567890
Topic '/test/std_msgs/string': timestamp mismatch: Rust=1234567890, Python=1234567891
```

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Include both positive and negative test cases
3. Add appropriate documentation and comments
4. Ensure tests are deterministic and repeatable
5. Update this README if adding new test categories

## Performance

The complete test suite typically runs in under 30 seconds, making it suitable for:
- Continuous integration pipelines
- Pre-commit validation
- Release verification
- Development testing

For performance benchmarking, use:
```bash
./scripts/run_comprehensive_validation.sh --performance
```
