# Self-Contained Test Suite for rosbag2-rs

This directory contains a comprehensive, self-contained test suite that validates the Rust rosbag2-rs implementation without requiring external dependencies or Python installations.

## Overview

The test suite provides:

1. **Self-contained validation** - No Python dependencies required
2. **Comprehensive coverage** - All 94 ROS2 message types validated
3. **Cross-format testing** - Both SQLite3 and MCAP formats
4. **Reference data validation** - Based on Python rosbags library ground truth
5. **Performance testing** - Optimized for CI/CD pipelines

## Test Files

### Core Test Files

- `integration_tests.rs` - Main integration test suite with comprehensive validation
- `README.md` - This documentation

### Test Data

The tests use permanent test bag fixtures located at:
- `bags/test_bags/test_bag_sqlite3` - SQLite3 format test bag
- `bags/test_bags/test_bag_mcap` - MCAP format test bag

Each bag contains:
- **94 topics** covering all major ROS2 message types
- **188 messages** total (2 messages per topic)
- **6 message categories**: geometry_msgs (29), sensor_msgs (27), std_msgs (30), nav_msgs (5), stereo_msgs (1), tf2_msgs (2)

## Test Categories

### 1. Basic Functionality Tests

- `test_read_sqlite3_bag()` - Verifies SQLite3 bag can be opened and read
- `test_read_mcap_bag()` - Verifies MCAP bag can be opened and read (when feature enabled)
- `test_all_messages_readable()` - Ensures all 188 messages can be read without errors

### 2. Reference Data Validation Tests

- `test_sqlite3_message_validation()` - Validates SQLite3 messages against reference data
- `test_mcap_message_validation()` - Validates MCAP messages against reference data

These tests use embedded reference data extracted from the Python rosbags library to ensure:
- Topic existence and correct message types
- Message data integrity
- Timestamp validity
- Proper message structure

### 3. Message Type Coverage Tests

- `test_comprehensive_message_type_coverage()` - Validates all 94 message types
- `test_specific_message_types()` - Tests common message types individually
- Covers all ROS2 message categories:
  - `geometry_msgs/*` - 3D geometry primitives, poses, transforms
  - `sensor_msgs/*` - Sensor data (images, point clouds, IMU, etc.)
  - `std_msgs/*` - Standard message types (strings, numbers, arrays)
  - `nav_msgs/*` - Navigation messages (maps, paths, odometry)
  - `stereo_msgs/*` - Stereo camera messages
  - `tf2_msgs/*` - Transform messages

### 4. Filtering and Query Tests

- `test_message_filtering_by_topic()` - Tests topic-based message filtering
- `test_message_filtering_by_timestamp()` - Tests time-based message filtering

### 5. Format Consistency Tests

- `test_bag_format_consistency()` - Ensures SQLite3 and MCAP contain identical data

## Running Tests

### Prerequisites

**No external dependencies required!** All tests are self-contained.

### Running Individual Tests

```bash
# Run basic functionality tests
cargo test --test integration_tests test_read_sqlite3_bag

# Run reference validation tests
cargo test --test integration_tests test_sqlite3_message_validation

# Run all integration tests
cargo test --test integration_tests
```

### Running All Tests

```bash
# Run complete test suite
cargo test

# Run with release optimizations
cargo test --release

# Run with MCAP support
cargo test --features mcap
```

## Reference Data

The test suite uses reference data extracted from the Python rosbags library as ground truth. This data is embedded directly in the Rust tests, ensuring:

1. **No external dependencies** - Tests run with just `cargo test`
2. **Consistent validation** - Same reference data used across all test runs
3. **Proven compatibility** - Based on established Python rosbags library
4. **Self-contained** - No network access or file downloads required

### Reference Data Structure

The embedded reference data includes:

- **Expected bag metadata** - Message counts, topic counts, storage identifiers
- **Topic information** - Topic names, message types, expected message counts
- **Sample message validation** - Ensures messages exist and have valid structure

## Expected Results

When all tests pass, you can be confident that:

- ✅ **Complete compatibility** - Rust produces identical results to Python rosbags
- ✅ **All message types supported** - 94 ROS2 message types validated
- ✅ **Format consistency** - SQLite3 and MCAP formats work identically
- ✅ **Data integrity** - All messages readable with valid timestamps
- ✅ **Filtering functionality** - Topic and timestamp filtering works correctly

## Test Performance

The complete test suite typically runs in under 10 seconds, making it suitable for:
- Continuous integration pipelines
- Pre-commit validation
- Release verification
- Development testing

### Performance Benchmarks

```bash
# Standard test run
cargo test --test integration_tests
# Expected time: ~5 seconds

# Release optimized test run
cargo test --test integration_tests --release
# Expected time: ~3 seconds

# Complete test suite
cargo test
# Expected time: ~10 seconds
```

## Troubleshooting

### Common Issues

1. **Test bag files missing**:
   - Ensure `bags/test_bags/` directory exists with both test bags
   - Check that bag files are not corrupted

2. **MCAP tests skipped**:
   - Enable MCAP feature: `cargo test --features mcap`
   - MCAP tests are optional and will be skipped if feature is not enabled

3. **Performance issues**:
   - Use release mode for faster execution: `cargo test --release`
   - Ensure sufficient disk space for temporary files

### Debugging Failed Tests

When tests fail, they provide detailed output showing:
- Which specific validation failed
- Expected vs actual values
- Specific topics or messages with issues

Example error output:
```
Topic count mismatch: expected 94, got 93
Expected topic '/test/geometry_msgs/point' not found
Message data is empty for topic '/test/std_msgs/string'
```

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Include both positive and negative test cases
3. Add appropriate documentation and comments
4. Ensure tests are deterministic and repeatable
5. Update this README if adding new test categories

## Self-Contained Design

This test suite is designed to be completely self-contained:

- ✅ **No Python dependencies** - Pure Rust implementation
- ✅ **No external scripts** - All logic in Rust test code
- ✅ **No network access** - All data embedded or in local files
- ✅ **No environment setup** - Works with standard `cargo test`
- ✅ **Reproducible** - Same results across different environments
- ✅ **Fast execution** - Optimized for CI/CD pipelines

This design ensures the tests can run reliably in any environment with just a Rust toolchain installed.
