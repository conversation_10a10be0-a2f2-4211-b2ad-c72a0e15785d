//! Comprehensive integration tests for rosbag2-rs with self-contained validation

use rosbag2_rs::{Reader, ReaderError};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Test bag file paths relative to the workspace root
const SQLITE3_BAG_PATH: &str = "bags/test_bags/test_bag_sqlite3";
const MCAP_BAG_PATH: &str = "bags/test_bags/test_bag_mcap";

/// Python reference data structure for a single message
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PythonMessageData {
    pub connection_id: u32,
    pub topic: String,
    pub msgtype: String,
    pub timestamp: u64,
    pub raw_data_hex: String,
    pub raw_data_size: usize,
    pub deserialized_data: Option<serde_json::Value>,
    pub deserialization_error: Option<String>,
}

/// Python reference data structure for topic information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PythonTopicInfo {
    pub id: u32,
    pub topic: String,
    pub msgtype: String,
    pub msgcount: u64,
    pub md5sum: String,
    pub serialization_format: String,
}

/// Python reference data structure for bag metadata
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct PythonBagMetadata {
    pub duration: u64,
    pub start_time: u64,
    pub end_time: u64,
    pub message_count: u64,
    pub compression_format: String,
    pub compression_mode: String,
}

/// Complete Python reference data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PythonReferenceData {
    pub bag_path: String,
    pub metadata: PythonBagMetadata,
    pub topics: Vec<PythonTopicInfo>,
    pub messages: Vec<PythonMessageData>,
}

/// Rust extracted data structure for comparison
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RustMessageData {
    pub connection_id: u32,
    pub topic: String,
    pub msgtype: String,
    pub timestamp: u64,
    pub raw_data_hex: String,
    pub raw_data_size: usize,
}

/// Rust extracted topic information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RustTopicInfo {
    pub id: u32,
    pub topic: String,
    pub msgtype: String,
    pub msgcount: u64,
    pub serialization_format: String,
}

/// Rust extracted bag metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RustBagMetadata {
    pub duration_ns: u64,
    pub start_time_ns: u64,
    pub end_time_ns: u64,
    pub message_count: u64,
    pub compression_format: String,
    pub storage_identifier: String,
}

/// Complete Rust data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RustExtractedData {
    pub bag_path: String,
    pub metadata: RustBagMetadata,
    pub topics: Vec<RustTopicInfo>,
    pub messages: Vec<RustMessageData>,
}

/// Validation results
#[derive(Debug, Clone)]
pub struct ValidationResults {
    pub metadata_matches: bool,
    pub topic_count_matches: bool,
    pub message_count_matches: bool,
    pub raw_data_matches: bool,
    pub timestamp_matches: bool,
    pub topic_mismatches: Vec<String>,
    pub message_mismatches: Vec<String>,
    pub errors: Vec<String>,
}

impl ValidationResults {
    pub fn new() -> Self {
        Self {
            metadata_matches: true,
            topic_count_matches: true,
            message_count_matches: true,
            raw_data_matches: true,
            timestamp_matches: true,
            topic_mismatches: Vec::new(),
            message_mismatches: Vec::new(),
            errors: Vec::new(),
        }
    }

    pub fn is_valid(&self) -> bool {
        self.metadata_matches
            && self.topic_count_matches
            && self.message_count_matches
            && self.raw_data_matches
            && self.timestamp_matches
            && self.topic_mismatches.is_empty()
            && self.message_mismatches.is_empty()
            && self.errors.is_empty()
    }

    pub fn add_error(&mut self, error: String) {
        self.errors.push(error);
    }

    pub fn add_topic_mismatch(&mut self, mismatch: String) {
        self.topic_mismatches.push(mismatch);
        self.topic_count_matches = false;
    }

    pub fn add_message_mismatch(&mut self, mismatch: String) {
        self.message_mismatches.push(mismatch);
        self.message_count_matches = false;
    }
}

/// Extract data from a bag file using the Rust implementation
fn extract_rust_data(bag_path: &str) -> Result<RustExtractedData, ReaderError> {
    let mut reader = Reader::new(bag_path)?;
    reader.open()?;

    let metadata = reader.metadata().unwrap();
    let info = metadata.info();

    // Extract metadata
    let rust_metadata = RustBagMetadata {
        duration_ns: info.duration.nanoseconds,
        start_time_ns: info.starting_time.nanoseconds_since_epoch,
        end_time_ns: info.starting_time.nanoseconds_since_epoch + info.duration.nanoseconds,
        message_count: info.message_count,
        compression_format: info.compression_format.clone(),
        storage_identifier: info.storage_identifier.clone(),
    };

    // Extract topic information
    let mut rust_topics = Vec::new();
    for connection in reader.connections() {
        let topic_info = RustTopicInfo {
            id: connection.id,
            topic: connection.topic.clone(),
            msgtype: connection.msgtype().to_string(),
            msgcount: connection.msgcount(),
            serialization_format: "cdr".to_string(), // ROS2 uses CDR
        };
        rust_topics.push(topic_info);
    }

    // Extract all messages
    let mut rust_messages = Vec::new();
    for message_result in reader.messages()? {
        let message = message_result?;
        
        // Find the connection for this message
        let connection = reader
            .connections()
            .iter()
            .find(|c| c.topic == message.topic)
            .ok_or_else(|| ReaderError::generic("Connection not found for message"))?;

        let message_data = RustMessageData {
            connection_id: connection.id,
            topic: message.topic.clone(),
            msgtype: connection.msgtype().to_string(),
            timestamp: message.timestamp,
            raw_data_hex: hex::encode(&message.data),
            raw_data_size: message.data.len(),
        };
        rust_messages.push(message_data);
    }

    Ok(RustExtractedData {
        bag_path: bag_path.to_string(),
        metadata: rust_metadata,
        topics: rust_topics,
        messages: rust_messages,
    })
}

/// Generate Python reference data for a bag file
fn generate_python_reference(bag_path: &str) -> Result<PythonReferenceData, Box<dyn std::error::Error>> {
    let temp_dir = TempDir::new()?;
    let output_path = temp_dir.path().join("python_reference.json");

    // Run the Python reference script
    let output = Command::new("bash")
        .arg("-c")
        .arg(&format!("source venv/bin/activate && python tests/python_reference.py {} {}", bag_path, output_path.display()))
        .output()?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        let stdout = String::from_utf8_lossy(&output.stdout);
        return Err(format!("Python reference script failed:\nstdout: {}\nstderr: {}", stdout, stderr).into());
    }

    // Read the generated JSON
    let json_content = std::fs::read_to_string(&output_path)?;
    let python_data: PythonReferenceData = serde_json::from_str(&json_content)?;

    Ok(python_data)
}

/// Test that we can successfully open and read the SQLite3 test bag
#[test]
fn test_read_sqlite3_bag() {
    let mut reader = Reader::new(SQLITE3_BAG_PATH).expect("Failed to create reader");
    reader.open().expect("Failed to open bag");

    assert!(reader.is_open());
    
    let metadata = reader.metadata().expect("Failed to get metadata");
    let info = metadata.info();
    
    // Verify basic metadata
    assert_eq!(info.storage_identifier, "sqlite3");
    assert_eq!(info.message_count, 188); // 2 messages per topic * 94 topics
    
    // Verify we have the expected number of topics (94 message types)
    let connections = reader.connections();
    assert_eq!(connections.len(), 94);
    
    // Verify we can read all messages
    let mut message_count = 0;
    for message_result in reader.messages().expect("Failed to get messages") {
        let _message = message_result.expect("Failed to read message");
        message_count += 1;
    }
    assert_eq!(message_count, 188);
}

/// Test that we can successfully open and read the MCAP test bag
#[test]
#[cfg(feature = "mcap")]
fn test_read_mcap_bag() {
    let mut reader = Reader::new(MCAP_BAG_PATH).expect("Failed to create reader");
    reader.open().expect("Failed to open bag");

    assert!(reader.is_open());
    
    let metadata = reader.metadata().expect("Failed to get metadata");
    let info = metadata.info();
    
    // Verify basic metadata
    assert_eq!(info.storage_identifier, "mcap");
    assert_eq!(info.message_count, 188); // 2 messages per topic * 94 topics
    
    // Verify we have the expected number of topics (94 message types)
    let connections = reader.connections();
    assert_eq!(connections.len(), 94);
    
    // Verify we can read all messages
    let mut message_count = 0;
    for message_result in reader.messages().expect("Failed to get messages") {
        let _message = message_result.expect("Failed to read message");
        message_count += 1;
    }
    assert_eq!(message_count, 188);
}

/// Cross-validation between Rust and Python implementations
fn validate_against_python_reference(
    rust_data: &RustExtractedData,
    python_data: &PythonReferenceData,
) -> ValidationResults {
    let mut results = ValidationResults::new();

    // Validate message counts
    if rust_data.messages.len() != python_data.messages.len() {
        results.message_count_matches = false;
        results.add_error(format!(
            "Message count mismatch: Rust={}, Python={}",
            rust_data.messages.len(),
            python_data.messages.len()
        ));
    }

    // Validate topic counts
    if rust_data.topics.len() != python_data.topics.len() {
        results.topic_count_matches = false;
        results.add_error(format!(
            "Topic count mismatch: Rust={}, Python={}",
            rust_data.topics.len(),
            python_data.topics.len()
        ));
    }

    // Create lookup maps for efficient comparison
    let mut python_messages_by_topic: HashMap<String, Vec<&PythonMessageData>> = HashMap::new();
    for msg in &python_data.messages {
        python_messages_by_topic
            .entry(msg.topic.clone())
            .or_insert_with(Vec::new)
            .push(msg);
    }

    let mut rust_messages_by_topic: HashMap<String, Vec<&RustMessageData>> = HashMap::new();
    for msg in &rust_data.messages {
        rust_messages_by_topic
            .entry(msg.topic.clone())
            .or_insert_with(Vec::new)
            .push(msg);
    }

    // Validate messages topic by topic
    for (topic, rust_msgs) in &rust_messages_by_topic {
        if let Some(python_msgs) = python_messages_by_topic.get(topic) {
            if rust_msgs.len() != python_msgs.len() {
                results.add_message_mismatch(format!(
                    "Topic '{}': message count mismatch: Rust={}, Python={}",
                    topic,
                    rust_msgs.len(),
                    python_msgs.len()
                ));
                continue;
            }

            // Sort messages by timestamp for comparison
            let mut rust_sorted = rust_msgs.clone();
            rust_sorted.sort_by_key(|m| m.timestamp);
            let mut python_sorted = python_msgs.clone();
            python_sorted.sort_by_key(|m| m.timestamp);

            // Compare each message
            for (rust_msg, python_msg) in rust_sorted.iter().zip(python_sorted.iter()) {
                // Compare timestamps
                if rust_msg.timestamp != python_msg.timestamp {
                    results.timestamp_matches = false;
                    results.add_message_mismatch(format!(
                        "Topic '{}': timestamp mismatch: Rust={}, Python={}",
                        topic, rust_msg.timestamp, python_msg.timestamp
                    ));
                }

                // Compare raw data
                if rust_msg.raw_data_hex != python_msg.raw_data_hex {
                    results.raw_data_matches = false;
                    results.add_message_mismatch(format!(
                        "Topic '{}': raw data mismatch at timestamp {}",
                        topic, rust_msg.timestamp
                    ));
                }

                // Compare data size
                if rust_msg.raw_data_size != python_msg.raw_data_size {
                    results.add_message_mismatch(format!(
                        "Topic '{}': data size mismatch: Rust={}, Python={}",
                        topic, rust_msg.raw_data_size, python_msg.raw_data_size
                    ));
                }
            }
        } else {
            results.add_topic_mismatch(format!(
                "Topic '{}' found in Rust but not in Python reference",
                topic
            ));
        }
    }

    // Check for topics in Python but not in Rust
    for topic in python_messages_by_topic.keys() {
        if !rust_messages_by_topic.contains_key(topic) {
            results.add_topic_mismatch(format!(
                "Topic '{}' found in Python but not in Rust",
                topic
            ));
        }
    }

    results
}

/// Cross-validation test for SQLite3 format against Python reference
#[test]
fn test_sqlite3_cross_validation() {
    // Extract data using Rust implementation
    let rust_data = extract_rust_data(SQLITE3_BAG_PATH)
        .expect("Failed to extract data with Rust implementation");

    // Generate Python reference data
    let python_data = generate_python_reference(SQLITE3_BAG_PATH)
        .expect("Failed to generate Python reference data");

    // Perform cross-validation
    let validation_results = validate_against_python_reference(&rust_data, &python_data);

    // Print detailed results for debugging
    if !validation_results.is_valid() {
        eprintln!("Validation failed for SQLite3 bag:");
        eprintln!("Metadata matches: {}", validation_results.metadata_matches);
        eprintln!("Topic count matches: {}", validation_results.topic_count_matches);
        eprintln!("Message count matches: {}", validation_results.message_count_matches);
        eprintln!("Raw data matches: {}", validation_results.raw_data_matches);
        eprintln!("Timestamp matches: {}", validation_results.timestamp_matches);

        for error in &validation_results.errors {
            eprintln!("Error: {}", error);
        }
        for mismatch in &validation_results.topic_mismatches {
            eprintln!("Topic mismatch: {}", mismatch);
        }
        for mismatch in &validation_results.message_mismatches {
            eprintln!("Message mismatch: {}", mismatch);
        }
    }

    // Assert that validation passes
    assert!(
        validation_results.is_valid(),
        "Cross-validation failed for SQLite3 bag file"
    );
}

/// Cross-validation test for MCAP format against Python reference
#[test]
#[cfg(feature = "mcap")]
fn test_mcap_cross_validation() {
    // Extract data using Rust implementation
    let rust_data = extract_rust_data(MCAP_BAG_PATH)
        .expect("Failed to extract data with Rust implementation");

    // Generate Python reference data
    let python_data = generate_python_reference(MCAP_BAG_PATH)
        .expect("Failed to generate Python reference data");

    // Perform cross-validation
    let validation_results = validate_against_python_reference(&rust_data, &python_data);

    // Print detailed results for debugging
    if !validation_results.is_valid() {
        eprintln!("Validation failed for MCAP bag:");
        eprintln!("Metadata matches: {}", validation_results.metadata_matches);
        eprintln!("Topic count matches: {}", validation_results.topic_count_matches);
        eprintln!("Message count matches: {}", validation_results.message_count_matches);
        eprintln!("Raw data matches: {}", validation_results.raw_data_matches);
        eprintln!("Timestamp matches: {}", validation_results.timestamp_matches);

        for error in &validation_results.errors {
            eprintln!("Error: {}", error);
        }
        for mismatch in &validation_results.topic_mismatches {
            eprintln!("Topic mismatch: {}", mismatch);
        }
        for mismatch in &validation_results.message_mismatches {
            eprintln!("Message mismatch: {}", mismatch);
        }
    }

    // Assert that validation passes
    assert!(
        validation_results.is_valid(),
        "Cross-validation failed for MCAP bag file"
    );
}

/// Test message filtering by topic
#[test]
fn test_message_filtering_by_topic() {
    let mut reader = Reader::new(SQLITE3_BAG_PATH).expect("Failed to create reader");
    reader.open().expect("Failed to open bag");

    let connections = reader.connections();

    // Test filtering by a specific topic
    let test_topic = "/test/std_msgs/string";
    let filtered_connections: Vec<_> = connections
        .iter()
        .filter(|c| c.topic == test_topic)
        .cloned()
        .collect();

    assert_eq!(filtered_connections.len(), 1);

    // Read messages for this specific topic
    let mut message_count = 0;
    for message_result in reader.messages_filtered(Some(&filtered_connections), None, None)
        .expect("Failed to get filtered messages") {
        let message = message_result.expect("Failed to read message");
        assert_eq!(message.topic, test_topic);
        message_count += 1;
    }

    // Should have exactly 2 messages for this topic
    assert_eq!(message_count, 2);
}

/// Test message filtering by timestamp range
#[test]
fn test_message_filtering_by_timestamp() {
    let mut reader = Reader::new(SQLITE3_BAG_PATH).expect("Failed to create reader");
    reader.open().expect("Failed to open bag");

    // Get all messages first to determine timestamp range
    let mut all_timestamps = Vec::new();
    for message_result in reader.messages().expect("Failed to get messages") {
        let message = message_result.expect("Failed to read message");
        all_timestamps.push(message.timestamp);
    }

    all_timestamps.sort();
    let min_timestamp = all_timestamps[0];
    let max_timestamp = all_timestamps[all_timestamps.len() - 1];
    let mid_timestamp = (min_timestamp + max_timestamp) / 2;

    // Test filtering by timestamp range (first half)
    let mut message_count = 0;
    for message_result in reader.messages_filtered(None, Some(min_timestamp), Some(mid_timestamp))
        .expect("Failed to get filtered messages") {
        let message = message_result.expect("Failed to read message");
        assert!(message.timestamp >= min_timestamp);
        assert!(message.timestamp <= mid_timestamp);
        message_count += 1;
    }

    // Should have some messages in the first half
    assert!(message_count > 0);
    assert!(message_count < all_timestamps.len());
}

/// Test that both bag formats contain identical message types
#[test]
#[cfg(feature = "mcap")]
fn test_bag_format_consistency() {
    // Read SQLite3 bag
    let mut sqlite_reader = Reader::new(SQLITE3_BAG_PATH).expect("Failed to create SQLite reader");
    sqlite_reader.open().expect("Failed to open SQLite bag");

    // Read MCAP bag
    let mut mcap_reader = Reader::new(MCAP_BAG_PATH).expect("Failed to create MCAP reader");
    mcap_reader.open().expect("Failed to open MCAP bag");

    // Get topic lists
    let sqlite_connections = sqlite_reader.connections();
    let mcap_connections = mcap_reader.connections();

    // Should have the same number of topics
    assert_eq!(sqlite_connections.len(), mcap_connections.len());

    // Create topic -> msgtype maps
    let sqlite_topics: HashMap<String, String> = sqlite_connections
        .iter()
        .map(|c| (c.topic.clone(), c.msgtype().to_string()))
        .collect();

    let mcap_topics: HashMap<String, String> = mcap_connections
        .iter()
        .map(|c| (c.topic.clone(), c.msgtype().to_string()))
        .collect();

    // Verify all topics exist in both bags with same message types
    for (topic, msgtype) in &sqlite_topics {
        assert!(
            mcap_topics.contains_key(topic),
            "Topic '{}' missing from MCAP bag",
            topic
        );
        assert_eq!(
            mcap_topics.get(topic).unwrap(),
            msgtype,
            "Message type mismatch for topic '{}': SQLite={}, MCAP={}",
            topic,
            msgtype,
            mcap_topics.get(topic).unwrap()
        );
    }
}

/// Test specific message types for correct deserialization
#[test]
fn test_specific_message_types() {
    let mut reader = Reader::new(SQLITE3_BAG_PATH).expect("Failed to create reader");
    reader.open().expect("Failed to open bag");

    let connections = reader.connections();

    // Test some specific message types that are commonly used
    let test_message_types = vec![
        "std_msgs/msg/String",
        "std_msgs/msg/Int32",
        "std_msgs/msg/Float64",
        "geometry_msgs/msg/Point",
        "geometry_msgs/msg/Pose",
        "sensor_msgs/msg/Image",
    ];

    for msgtype in test_message_types {
        let matching_connections: Vec<_> = connections
            .iter()
            .filter(|c| c.msgtype() == msgtype)
            .cloned()
            .collect();

        assert_eq!(
            matching_connections.len(), 1,
            "Expected exactly one connection for message type '{}'",
            msgtype
        );

        // Read messages for this message type
        let mut message_count = 0;
        for message_result in reader.messages_filtered(Some(&matching_connections), None, None)
            .expect("Failed to get filtered messages") {
            let message = message_result.expect("Failed to read message");

            // Verify message has data
            assert!(!message.data.is_empty(), "Message data is empty for type '{}'", msgtype);

            // Verify timestamp is reasonable (not zero, not too far in future)
            assert!(message.timestamp > 0, "Invalid timestamp for message type '{}'", msgtype);

            message_count += 1;
        }

        // Should have exactly 2 messages per topic
        assert_eq!(message_count, 2, "Expected 2 messages for type '{}'", msgtype);
    }
}

/// Test comprehensive coverage of all 94 message types
#[test]
fn test_comprehensive_message_type_coverage() {
    let mut reader = Reader::new(SQLITE3_BAG_PATH).expect("Failed to create reader");
    reader.open().expect("Failed to open bag");

    let connections = reader.connections();

    // Expected message type categories and counts
    let expected_categories = vec![
        ("geometry_msgs", 29),
        ("nav_msgs", 5),
        ("sensor_msgs", 27),
        ("std_msgs", 30),
        ("stereo_msgs", 1),
        ("tf2_msgs", 2),
    ];

    // Count message types by category
    let mut category_counts: HashMap<String, usize> = HashMap::new();
    for connection in connections {
        let msgtype = connection.msgtype();
        if let Some(category) = msgtype.split('/').next() {
            *category_counts.entry(category.to_string()).or_insert(0) += 1;
        }
    }

    // Verify we have the expected categories and counts
    for (category, expected_count) in expected_categories {
        let actual_count = category_counts.get(category).unwrap_or(&0);
        assert_eq!(
            *actual_count, expected_count,
            "Expected {} message types in category '{}', found {}",
            expected_count, category, actual_count
        );
    }

    // Verify total count
    let total_types: usize = category_counts.values().sum();
    assert_eq!(total_types, 94, "Expected 94 total message types, found {}", total_types);
}

/// Test that all messages can be read without errors
#[test]
fn test_all_messages_readable() {
    let mut reader = Reader::new(SQLITE3_BAG_PATH).expect("Failed to create reader");
    reader.open().expect("Failed to open bag");

    let mut message_count = 0;
    let mut topics_seen = std::collections::HashSet::new();

    for message_result in reader.messages().expect("Failed to get messages") {
        let message = message_result.expect("Failed to read message");

        // Track topics we've seen
        topics_seen.insert(message.topic.clone());

        // Verify message has reasonable properties
        assert!(!message.data.is_empty(), "Message data is empty for topic '{}'", message.topic);
        assert!(message.timestamp > 0, "Invalid timestamp for topic '{}'", message.topic);

        message_count += 1;
    }

    // Verify we read all expected messages
    assert_eq!(message_count, 188, "Expected 188 messages, read {}", message_count);

    // Verify we saw all expected topics
    assert_eq!(topics_seen.len(), 94, "Expected 94 unique topics, saw {}", topics_seen.len());
}
