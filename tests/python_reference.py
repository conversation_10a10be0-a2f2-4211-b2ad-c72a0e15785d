#!/usr/bin/env python3
"""
Python reference implementation for cross-validation with Rust rosbag reader.

This script extracts data from both SQLite3 and MCAP format ROS2 bag files
and outputs the results in a JSON format that can be compared with the Rust implementation.
"""

import json
import sys
from pathlib import Path
from typing import Any, Dict, List, Tuple

try:
    from rosbags.rosbag2 import Reader
    from rosbags.typesys import Stores, get_typestore
except ImportError as e:
    print(f'Error importing rosbags: {e}')
    print('Please install rosbags: pip install rosbags')
    sys.exit(1)


def extract_bag_data(bag_path: str) -> Dict[str, Any]:
    """
    Extract all data from a ROS2 bag file.
    
    Returns a dictionary with:
    - metadata: bag metadata information
    - topics: list of topic information
    - messages: list of all messages with their data
    """
    bag_path = Path(bag_path)
    typestore = get_typestore(Stores.ROS2_HUMBLE)
    
    result = {
        'bag_path': str(bag_path),
        'metadata': {},
        'topics': [],
        'messages': []
    }
    
    with Reader(bag_path) as reader:
        # Extract metadata
        result['metadata'] = {
            'duration': reader.duration,
            'start_time': reader.start_time,
            'end_time': reader.end_time,
            'message_count': reader.message_count,
            'compression_format': reader.compression_format or '',
            'compression_mode': reader.compression_mode or '',
        }
        
        # Extract topic information
        for connection in reader.connections:
            topic_info = {
                'id': connection.id,
                'topic': connection.topic,
                'msgtype': connection.msgtype,
                'msgcount': connection.msgcount,
                'md5sum': getattr(connection, 'md5sum', ''),
                'serialization_format': getattr(connection, 'serialization_format', 'cdr'),
            }
            result['topics'].append(topic_info)
        
        # Extract all messages
        for connection, timestamp, rawdata in reader.messages():
            try:
                # Deserialize the message using typestore
                msg = typestore.deserialize_cdr(rawdata, connection.msgtype)

                # Convert message to a serializable format
                msg_data = message_to_dict(msg)

                message_info = {
                    'connection_id': connection.id,
                    'topic': connection.topic,
                    'msgtype': connection.msgtype,
                    'timestamp': timestamp,
                    'raw_data_hex': rawdata.hex(),
                    'raw_data_size': len(rawdata),
                    'deserialized_data': msg_data,
                }
                result['messages'].append(message_info)

            except Exception as e:
                # Include failed messages for debugging
                message_info = {
                    'connection_id': connection.id,
                    'topic': connection.topic,
                    'msgtype': connection.msgtype,
                    'timestamp': timestamp,
                    'raw_data_hex': rawdata.hex(),
                    'raw_data_size': len(rawdata),
                    'deserialization_error': str(e),
                }
                result['messages'].append(message_info)
    
    return result


def message_to_dict(msg: Any) -> Dict[str, Any]:
    """
    Convert a ROS message to a dictionary representation.
    Handles nested messages and arrays.
    """
    if hasattr(msg, '__slots__'):
        # ROS message with slots
        result = {}
        for slot in msg.__slots__:
            value = getattr(msg, slot)
            result[slot] = serialize_value(value)
        return result
    else:
        # Simple value
        return serialize_value(msg)


def serialize_value(value: Any) -> Any:
    """
    Serialize a value to a JSON-compatible format.
    """
    if value is None:
        return None
    elif isinstance(value, (bool, int, float, str)):
        return value
    elif isinstance(value, bytes):
        return value.hex()
    elif isinstance(value, (list, tuple)):
        return [serialize_value(item) for item in value]
    elif hasattr(value, '__slots__'):
        # Nested ROS message
        return message_to_dict(value)
    elif hasattr(value, 'tolist'):
        # NumPy array
        return value.tolist()
    else:
        # Try to convert to string as fallback
        return str(value)


def main():
    if len(sys.argv) != 3:
        print("Usage: python python_reference.py <bag_path> <output_json>")
        sys.exit(1)
    
    bag_path = sys.argv[1]
    output_path = sys.argv[2]
    
    try:
        print(f"Extracting data from bag: {bag_path}")
        data = extract_bag_data(bag_path)
        
        print(f"Found {len(data['topics'])} topics and {len(data['messages'])} messages")
        
        # Write results to JSON file
        with open(output_path, 'w') as f:
            json.dump(data, f, indent=2, sort_keys=True)
        
        print(f"Results written to: {output_path}")
        
    except Exception as e:
        print(f"Error processing bag: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()
