#!/usr/bin/env python3
"""
Extract GPS data with proper deserialization to compare with Rust implementation.
"""

from rosbags.rosbag2 import Reader
from rosbags.serde import deserialize_cdr
from pathlib import Path
import sys

def extract_gps_structured(bag_path, output_file):
    """Extract GPS data with proper NavSatFix deserialization."""
    print(f"Extracting GPS data from: {bag_path}")
    print(f"Output file: {output_file}")
    
    try:
        with Reader(bag_path) as reader:
            # Find GPS topic
            gps_connection = None
            for connection in reader.connections:
                if connection.topic == "/ros_ap_forwarder/gps":
                    gps_connection = connection
                    break
            
            if not gps_connection:
                print("Error: GPS topic not found")
                return False
            
            print(f"Found GPS topic: {gps_connection.topic}")
            print(f"Message type: {gps_connection.msgtype}")
            print(f"Message count: {gps_connection.msgcount}")
            
            message_count = 0
            
            with open(output_file, 'w') as f:
                # Write header
                f.write("# Format: timestamp_ns,header_sec,header_nanosec,frame_id,status,service,latitude,longitude,altitude,position_covariance_type\n")
                
                # Read and deserialize messages
                for (connection, timestamp, rawdata) in reader.messages([gps_connection]):
                    try:
                        # Deserialize the CDR data
                        msg = deserialize_cdr(rawdata, connection.msgtype)
                        
                        # Extract NavSatFix fields
                        header_sec = msg.header.stamp.sec
                        header_nanosec = msg.header.stamp.nanosec
                        frame_id = msg.header.frame_id
                        status = msg.status.status
                        service = msg.status.service
                        latitude = msg.latitude
                        longitude = msg.longitude
                        altitude = msg.altitude
                        position_covariance_type = msg.position_covariance_type
                        
                        # Write structured data
                        f.write(f"{timestamp},{header_sec},{header_nanosec},{frame_id},{status},{service},{latitude:.6f},{longitude:.6f},{altitude:.6f},{position_covariance_type}\n")
                        
                        message_count += 1
                        if message_count % 1000 == 0:
                            print(f"Processed {message_count} messages...")
                            
                    except Exception as e:
                        print(f"Warning: Failed to deserialize message {message_count + 1}: {e}")
                        continue
            
            print(f"Successfully extracted {message_count} GPS messages")
            return True
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    bag_path = Path("../scaled_07_30_00")
    output_file = "python_gps_structured.txt"
    
    print("Python rosbags GPS extraction with proper deserialization")
    print("=" * 60)
    
    success = extract_gps_structured(bag_path, output_file)
    if success:
        print(f"\n✅ GPS data extracted successfully to: {output_file}")
    else:
        print(f"\n❌ Failed to extract GPS data")

if __name__ == "__main__":
    main()
