{"bag_path": "bags/test_bags/test_bag_mcap", "messages": [{"connection_id": 1, "deserialized_data": "geometry_msgs__msg__Accel(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Accel')", "msgtype": "geometry_msgs/msg/Accel", "raw_data_hex": "000100009a9999999999b93f9a9999999999c93f9a99999999992340000000000000000000000000000000009a9999999999b93f", "raw_data_size": 52, "timestamp": **********903302144, "topic": "/test/geometry_msgs/accel"}, {"connection_id": 2, "deserialized_data": "geometry_msgs__msg__AccelStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=905135360, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), accel=geometry_msgs__msg__Accel(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Accel'), __msgtype__='geometry_msgs/msg/AccelStamped')", "msgtype": "geometry_msgs/msg/AccelStamped", "raw_data_hex": "00010000fc6e44680045f3350b000000746573745f6672616d6500009a9999999999b93f9a9999999999c93f9a99999999992340000000000000000000000000000000009a9999999999b93f", "raw_data_size": 76, "timestamp": **********003302144, "topic": "/test/geometry_msgs/accel_stamped"}, {"connection_id": 3, "deserialized_data": "geometry_msgs__msg__AccelWithCovariance(accel=geometry_msgs__msg__Accel(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Accel'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/AccelWithCovariance')", "msgtype": "geometry_msgs/msg/AccelWithCovariance", "raw_data_hex": "000100009a9999999999b93f9a9999999999c93f9a99999999992340000000000000000000000000000000009a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 340, "timestamp": **********103302144, "topic": "/test/geometry_msgs/accel_with_covariance"}, {"connection_id": 4, "deserialized_data": "geometry_msgs__msg__AccelWithCovarianceStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=908849664, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), accel=geometry_msgs__msg__AccelWithCovariance(accel=geometry_msgs__msg__Accel(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Accel'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/AccelWithCovariance'), __msgtype__='geometry_msgs/msg/AccelWithCovarianceStamped')", "msgtype": "geometry_msgs/msg/AccelWithCovarianceStamped", "raw_data_hex": "00010000fc6e446800f22b360b000000746573745f6672616d6500009a9999999999b93f9a9999999999c93f9a99999999992340000000000000000000000000000000009a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 364, "timestamp": **********203302144, "topic": "/test/geometry_msgs/accel_with_covariance_stamped"}, {"connection_id": 5, "deserialized_data": "geometry_msgs__msg__Inertia(m=1.5, com=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), ixx=0.1, ixy=0.0, ixz=0.0, iyy=0.1, iyz=0.0, izz=0.1, __msgtype__='geometry_msgs/msg/Inertia')", "msgtype": "geometry_msgs/msg/Inertia", "raw_data_hex": "00010000000000000000f83f0000000000000000000000000000000000000000000000009a9999999999b93f000000000000000000000000000000009a9999999999b93f00000000000000009a9999999999b93f", "raw_data_size": 84, "timestamp": **********303302144, "topic": "/test/geometry_msgs/inertia"}, {"connection_id": 6, "deserialized_data": "geometry_msgs__msg__InertiaStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=911190784, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), inertia=geometry_msgs__msg__Inertia(m=1.5, com=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), ixx=0.1, ixy=0.0, ixz=0.0, iyy=0.1, iyz=0.0, izz=0.1, __msgtype__='geometry_msgs/msg/Inertia'), __msgtype__='geometry_msgs/msg/InertiaStamped')", "msgtype": "geometry_msgs/msg/InertiaStamped", "raw_data_hex": "00010000fc6e446800ab4f360b000000746573745f6672616d650000000000000000f83f0000000000000000000000000000000000000000000000009a9999999999b93f000000000000000000000000000000009a9999999999b93f00000000000000009a9999999999b93f", "raw_data_size": 108, "timestamp": **********403302144, "topic": "/test/geometry_msgs/inertia_stamped"}, {"connection_id": 7, "deserialized_data": "geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point')", "msgtype": "geometry_msgs/msg/Point", "raw_data_hex": "00010000000000000000f03f00000000000000400000000000000840", "raw_data_size": 28, "timestamp": **********503302144, "topic": "/test/geometry_msgs/point"}, {"connection_id": 8, "deserialized_data": "geometry_msgs__msg__Point32(x=1.5, y=2.5, z=3.5, __msgtype__='geometry_msgs/msg/Point32')", "msgtype": "geometry_msgs/msg/Point32", "raw_data_hex": "000100000000c03f0000204000006040", "raw_data_size": 16, "timestamp": **********603302144, "topic": "/test/geometry_msgs/point32"}, {"connection_id": 9, "deserialized_data": "geometry_msgs__msg__PointStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=913859584, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), point=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), __msgtype__='geometry_msgs/msg/PointStamped')", "msgtype": "geometry_msgs/msg/PointStamped", "raw_data_hex": "00010000fc6e4468006478360b000000746573745f6672616d650000000000000000f03f00000000000000400000000000000840", "raw_data_size": 52, "timestamp": **********703302144, "topic": "/test/geometry_msgs/point_stamped"}, {"connection_id": 10, "deserialized_data": "geometry_msgs__msg__Polygon(points=[geometry_msgs__msg__Point32(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=0.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32')], __msgtype__='geometry_msgs/msg/Polygon')", "msgtype": "geometry_msgs/msg/Polygon", "raw_data_hex": "00010000040000000000000000000000000000000000803f00000000000000000000803f0000803f00000000000000000000803f00000000", "raw_data_size": 56, "timestamp": **********803302144, "topic": "/test/geometry_msgs/polygon"}, {"connection_id": 11, "deserialized_data": "geometry_msgs__msg__PolygonStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=915824128, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), polygon=geometry_msgs__msg__Polygon(points=[geometry_msgs__msg__Point32(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=0.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32')], __msgtype__='geometry_msgs/msg/Polygon'), __msgtype__='geometry_msgs/msg/PolygonStamped')", "msgtype": "geometry_msgs/msg/PolygonStamped", "raw_data_hex": "00010000fc6e4468005e96360b000000746573745f6672616d650000040000000000000000000000000000000000803f00000000000000000000803f0000803f00000000000000000000803f00000000", "raw_data_size": 80, "timestamp": **********903302144, "topic": "/test/geometry_msgs/polygon_stamped"}, {"connection_id": 12, "deserialized_data": "geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose')", "msgtype": "geometry_msgs/msg/Pose", "raw_data_hex": "00010000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 60, "timestamp": 1749315326003302144, "topic": "/test/geometry_msgs/pose"}, {"connection_id": 13, "deserialized_data": "geometry_msgs__msg__Pose2D(x=1.0, y=2.0, theta=0.5, __msgtype__='geometry_msgs/msg/Pose2D')", "msgtype": "geometry_msgs/msg/Pose2D", "raw_data_hex": "00010000000000000000f03f0000000000000040000000000000e03f", "raw_data_size": 28, "timestamp": 1749315326103302144, "topic": "/test/geometry_msgs/pose2d"}, {"connection_id": 14, "deserialized_data": "geometry_msgs__msg__PoseArray(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=919361280, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), poses=[geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose')], __msgtype__='geometry_msgs/msg/PoseArray')", "msgtype": "geometry_msgs/msg/PoseArray", "raw_data_hex": "00010000fc6e44680057cc360b000000746573745f6672616d6500000200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03f000000000000f03f000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 148, "timestamp": 1749315326203302144, "topic": "/test/geometry_msgs/pose_array"}, {"connection_id": 15, "deserialized_data": "geometry_msgs__msg__PoseStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=920854016, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='geometry_msgs/msg/PoseStamped')", "msgtype": "geometry_msgs/msg/PoseStamped", "raw_data_hex": "00010000fc6e4468001ee3360b000000746573745f6672616d650000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 84, "timestamp": 1749315326303302144, "topic": "/test/geometry_msgs/pose_stamped"}, {"connection_id": 16, "deserialized_data": "geometry_msgs__msg__PoseWithCovariance(pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/PoseWithCovariance')", "msgtype": "geometry_msgs/msg/PoseWithCovariance", "raw_data_hex": "00010000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 348, "timestamp": 1749315326403302144, "topic": "/test/geometry_msgs/pose_with_covariance"}, {"connection_id": 17, "deserialized_data": "geometry_msgs__msg__PoseWithCovarianceStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=922751488, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), pose=geometry_msgs__msg__PoseWithCovariance(pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/PoseWithCovariance'), __msgtype__='geometry_msgs/msg/PoseWithCovarianceStamped')", "msgtype": "geometry_msgs/msg/PoseWithCovarianceStamped", "raw_data_hex": "00010000fc6e4468001200370b000000746573745f6672616d650000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 372, "timestamp": 1749315326503302144, "topic": "/test/geometry_msgs/pose_with_covariance_stamped"}, {"connection_id": 18, "deserialized_data": "geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion')", "msgtype": "geometry_msgs/msg/Quaternion", "raw_data_hex": "00010000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 36, "timestamp": 1749315326603302144, "topic": "/test/geometry_msgs/quaternion"}, {"connection_id": 19, "deserialized_data": "geometry_msgs__msg__QuaternionStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=923759104, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), quaternion=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/QuaternionStamped')", "msgtype": "geometry_msgs/msg/QuaternionStamped", "raw_data_hex": "00010000fc6e446800720f370b000000746573745f6672616d650000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 60, "timestamp": 1749315326703302144, "topic": "/test/geometry_msgs/quaternion_stamped"}, {"connection_id": 20, "deserialized_data": "geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform')", "msgtype": "geometry_msgs/msg/Transform", "raw_data_hex": "00010000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 60, "timestamp": 1749315326803302144, "topic": "/test/geometry_msgs/transform"}, {"connection_id": 21, "deserialized_data": "geometry_msgs__msg__TransformStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=925613312, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), child_frame_id='child_frame', transform=geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform'), __msgtype__='geometry_msgs/msg/TransformStamped')", "msgtype": "geometry_msgs/msg/TransformStamped", "raw_data_hex": "00010000fc6e446800bd2b370b000000746573745f6672616d6500000c0000006368696c645f6672616d6500000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 100, "timestamp": 1749315326903302144, "topic": "/test/geometry_msgs/transform_stamped"}, {"connection_id": 22, "deserialized_data": "geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist')", "msgtype": "geometry_msgs/msg/Twist", "raw_data_hex": "00010000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000e03f", "raw_data_size": 52, "timestamp": 1749315327003302144, "topic": "/test/geometry_msgs/twist"}, {"connection_id": 23, "deserialized_data": "geometry_msgs__msg__TwistStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=927704576, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), twist=geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), __msgtype__='geometry_msgs/msg/TwistStamped')", "msgtype": "geometry_msgs/msg/TwistStamped", "raw_data_hex": "00010000fc6e446800a64b370b000000746573745f6672616d650000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000e03f", "raw_data_size": 76, "timestamp": 1749315327103302144, "topic": "/test/geometry_msgs/twist_stamped"}, {"connection_id": 24, "deserialized_data": "geometry_msgs__msg__TwistWithCovariance(twist=geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01]), __msgtype__='geometry_msgs/msg/TwistWithCovariance')", "msgtype": "geometry_msgs/msg/TwistWithCovariance", "raw_data_hex": "00010000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000e03f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f", "raw_data_size": 340, "timestamp": 1749315327203302144, "topic": "/test/geometry_msgs/twist_with_covariance"}, {"connection_id": 25, "deserialized_data": "geometry_msgs__msg__TwistWithCovarianceStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=929635328, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), twist=geometry_msgs__msg__TwistWithCovariance(twist=geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01]), __msgtype__='geometry_msgs/msg/TwistWithCovariance'), __msgtype__='geometry_msgs/msg/TwistWithCovarianceStamped')", "msgtype": "geometry_msgs/msg/TwistWithCovarianceStamped", "raw_data_hex": "00010000fc6e4468001c69370b000000746573745f6672616d650000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000e03f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f", "raw_data_size": 364, "timestamp": 1749315327303302144, "topic": "/test/geometry_msgs/twist_with_covariance_stamped"}, {"connection_id": 26, "deserialized_data": "geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3, __msgtype__='geometry_msgs/msg/Vector3')", "msgtype": "geometry_msgs/msg/Vector3", "raw_data_hex": "000100009a9999999999b93f9a9999999999c93f333333333333d33f", "raw_data_size": 28, "timestamp": 1749315327403302144, "topic": "/test/geometry_msgs/vector3"}, {"connection_id": 27, "deserialized_data": "geometry_msgs__msg__Vector3Stamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=930620160, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), vector=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Vector3Stamped')", "msgtype": "geometry_msgs/msg/Vector3Stamped", "raw_data_hex": "00010000fc6e4468002378370b000000746573745f6672616d650000000000000000f03f00000000000000400000000000000840", "raw_data_size": 52, "timestamp": 1749315327503302144, "topic": "/test/geometry_msgs/vector3_stamped"}, {"connection_id": 28, "deserialized_data": "geometry_msgs__msg__Wrench(force=geometry_msgs__msg__Vector3(x=10.0, y=5.0, z=2.0, __msgtype__='geometry_msgs/msg/Vector3'), torque=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Wrench')", "msgtype": "geometry_msgs/msg/Wrench", "raw_data_hex": "000100000000000000002440000000000000144000000000000000409a9999999999b93f9a9999999999c93f333333333333d33f", "raw_data_size": 52, "timestamp": 1749315327603302144, "topic": "/test/geometry_msgs/wrench"}, {"connection_id": 29, "deserialized_data": "geometry_msgs__msg__WrenchStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=932406528, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), wrench=geometry_msgs__msg__Wrench(force=geometry_msgs__msg__Vector3(x=10.0, y=5.0, z=2.0, __msgtype__='geometry_msgs/msg/Vector3'), torque=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Wrench'), __msgtype__='geometry_msgs/msg/WrenchStamped')", "msgtype": "geometry_msgs/msg/WrenchStamped", "raw_data_hex": "00010000fc6e4468006593370b000000746573745f6672616d6500000000000000002440000000000000144000000000000000409a9999999999b93f9a9999999999c93f333333333333d33f", "raw_data_size": 76, "timestamp": 1749315327703302144, "topic": "/test/geometry_msgs/wrench_stamped"}, {"connection_id": 30, "deserialized_data": "nav_msgs__msg__GridCells(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=933358336, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), cell_width=0.10000000149011612, cell_height=0.10000000149011612, cells=[geometry_msgs__msg__Point(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), geometry_msgs__msg__Point(x=2.0, y=2.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), geometry_msgs__msg__Point(x=3.0, y=3.0, z=0.0, __msgtype__='geometry_msgs/msg/Point')], __msgtype__='nav_msgs/msg/GridCells')", "msgtype": "nav_msgs/msg/GridCells", "raw_data_hex": "00010000fc6e446800eba1370b000000746573745f6672616d650000cdcccc3dcdcccc3d0300000000000000000000000000f03f000000000000f03f0000000000000000000000000000004000000000000000400000000000000000000000000000084000000000000008400000000000000000", "raw_data_size": 116, "timestamp": 1749315327803302144, "topic": "/test/nav_msgs/grid_cells"}, {"connection_id": 31, "deserialized_data": "nav_msgs__msg__MapMetaData(map_load_time=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), resolution=0.05000000074505806, width=10, height=10, origin=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='nav_msgs/msg/MapMetaData')", "msgtype": "nav_msgs/msg/MapMetaData", "raw_data_hex": "000100000000000000000000cdcc4c3d0a0000000a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 84, "timestamp": 1749315327903302144, "topic": "/test/nav_msgs/map_metadata"}, {"connection_id": 32, "deserialized_data": "nav_msgs__msg__OccupancyGrid(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=936107008, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), info=nav_msgs__msg__MapMetaData(map_load_time=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), resolution=0.05000000074505806, width=10, height=10, origin=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='nav_msgs/msg/MapMetaData'), data=array([-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1],\n      dtype=int8), __msgtype__='nav_msgs/msg/OccupancyGrid')", "msgtype": "nav_msgs/msg/OccupancyGrid", "raw_data_hex": "00010000fc6e446800dccb370b000000746573745f6672616d6500000000000000000000cdcc4c3d0a0000000a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03f64000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "raw_data_size": 212, "timestamp": 1749315328003302144, "topic": "/test/nav_msgs/occupancy_grid"}, {"connection_id": 33, "deserialized_data": "nav_msgs__msg__Odometry(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=937401600, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), child_frame_id='base_link', pose=geometry_msgs__msg__PoseWithCovariance(pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/PoseWithCovariance'), twist=geometry_msgs__msg__TwistWithCovariance(twist=geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01]), __msgtype__='geometry_msgs/msg/TwistWithCovariance'), __msgtype__='nav_msgs/msg/Odometry')", "msgtype": "nav_msgs/msg/Odometry", "raw_data_hex": "00010000fc6e4468009ddf370b000000746573745f6672616d6500000a000000626173655f6c696e6b000000000000000000f03f00000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000f03f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f000000000000f03f00000000000000000000000000000000000000000000000000000000000000009a9999999999b93f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f", "raw_data_size": 724, "timestamp": 1749315328103302144, "topic": "/test/nav_msgs/odometry"}, {"connection_id": 34, "deserialized_data": "nav_msgs__msg__Path(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=938809088, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), poses=[geometry_msgs__msg__PoseStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=938811392, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='geometry_msgs/msg/PoseStamped'), geometry_msgs__msg__PoseStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=938814720, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='geometry_msgs/msg/PoseStamped')], __msgtype__='nav_msgs/msg/Path')", "msgtype": "nav_msgs/msg/Path", "raw_data_hex": "00010000fc6e44680017f5370b000000746573745f6672616d65000002000000fc6e44680020f5370b000000746573745f6672616d65000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03ffc6e4468002df5370b000000746573745f6672616d650000000000000000f03f000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 196, "timestamp": 1749315328203302144, "topic": "/test/nav_msgs/path"}, {"connection_id": 35, "deserialized_data": "sensor_msgs__msg__BatteryState(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=940140032, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), voltage=12.600000381469727, temperature=25.0, current=-5.0, charge=50.0, capacity=100.0, design_capacity=100.0, percentage=0.5, power_supply_status=2, power_supply_health=1, power_supply_technology=1, present=True, cell_voltage=array([3.7, 3.7, 3.7], dtype=float32), cell_temperature=array([25., 25., 25.], dtype=float32), location='battery_compartment', serial_number='BAT123456', POWER_SUPPLY_STATUS_UNKNOWN=0, POWER_SUPPLY_STATUS_CHARGING=1, POWER_SUPPLY_STATUS_DISCHARGING=2, POWER_SUPPLY_STATUS_NOT_CHARGING=3, POWER_SUPPLY_STATUS_FULL=4, POWER_SUPPLY_HEALTH_UNKNOWN=0, POWER_SUPPLY_HEALTH_GOOD=1, POWER_SUPPLY_HEALTH_OVERHEAT=2, POWER_SUPPLY_HEALTH_DEAD=3, POWER_SUPPLY_HEALTH_OVERVOLTAGE=4, POWER_SUPPLY_HEALTH_UNSPEC_FAILURE=5, POWER_SUPPLY_HEALTH_COLD=6, POWER_SUPPLY_HEALTH_WATCHDOG_TIMER_EXPIRE=7, POWER_SUPPLY_HEALTH_SAFETY_TIMER_EXPIRE=8, POWER_SUPPLY_TECHNOLOGY_UNKNOWN=0, POWER_SUPPLY_TECHNOLOGY_NIMH=1, POWER_SUPPLY_TECHNOLOGY_LION=2, POWER_SUPPLY_TECHNOLOGY_LIPO=3, POWER_SUPPLY_TECHNOLOGY_LIFE=4, POWER_SUPPLY_TECHNOLOGY_NICD=5, POWER_SUPPLY_TECHNOLOGY_LIMN=6, __msgtype__='sensor_msgs/msg/BatteryState')", "msgtype": "sensor_msgs/msg/BatteryState", "raw_data_hex": "00010000fc6e4468006609380b000000746573745f6672616d6500009a9949410000c8410000a0c0000048420000c8420000c8420000003f0201010103000000cdcc6c40cdcc6c40cdcc6c40030000000000c8410000c8410000c84114000000626174746572795f636f6d706172746d656e74000a00000042415431323334353600", "raw_data_size": 130, "timestamp": 1749315328303302144, "topic": "/test/sensor_msgs/battery_state"}, {"connection_id": 36, "deserialized_data": "sensor_msgs__msg__CameraInfo(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=943284736, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), height=480, width=640, distortion_model='plumb_bob', d=array([ 0.1  , -0.2  ,  0.001,  0.002,  0.   ]), k=array([525.,   0., 320.,   0., 525., 240.,   0.,   0.,   1.]), r=array([1., 0., 0., 0., 1., 0., 0., 0., 1.]), p=array([525.,   0., 320.,   0.,   0., 525., 240.,   0.,   0.,   0.,   1.,\n         0.]), binning_x=1, binning_y=1, roi=sensor_msgs__msg__RegionOfInterest(x_offset=0, y_offset=0, height=0, width=0, do_rectify=False, __msgtype__='sensor_msgs/msg/RegionOfInterest'), __msgtype__='sensor_msgs/msg/CameraInfo')", "msgtype": "sensor_msgs/msg/CameraInfo", "raw_data_hex": "00010000fc6e4468006239380b000000746573745f6672616d650000e0010000800200000a000000706c756d625f626f6200000005000000000000009a9999999999b93f9a9999999999c9bffca9f1d24d62503ffca9f1d24d62603f0000000000000000000000000068804000000000000000000000000000007440000000000000000000000000006880400000000000006e4000000000000000000000000000000000000000000000f03f000000000000f03f000000000000000000000000000000000000000000000000000000000000f03f000000000000000000000000000000000000000000000000000000000000f03f0000000000688040000000000000000000000000000074400000000000000000000000000000000000000000006880400000000000006e40000000000000000000000000000000000000000000000000000000000000f03f000000000000000001000000010000000000000000000000000000000000000000", "raw_data_size": 365, "timestamp": 1749315328403302144, "topic": "/test/sensor_msgs/camera_info"}, {"connection_id": 37, "deserialized_data": "sensor_msgs__msg__ChannelFloat32(name='intensity', values=array([100., 200., 300.], dtype=float32), __msgtype__='sensor_msgs/msg/ChannelFloat32')", "msgtype": "sensor_msgs/msg/ChannelFloat32", "raw_data_hex": "000100000a000000696e74656e73697479000000030000000000c8420000484300009643", "raw_data_size": 36, "timestamp": 1749315328503302144, "topic": "/test/sensor_msgs/channel_float32"}, {"connection_id": 38, "deserialized_data": "sensor_msgs__msg__CompressedImage(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=948017664, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), format='jpeg', data=array([255, 216, 255, 224,   0,  16,  74,  70,  73,  70], dtype=uint8), __msgtype__='sensor_msgs/msg/CompressedImage')", "msgtype": "sensor_msgs/msg/CompressedImage", "raw_data_hex": "00010000fc6e4468009a81380b000000746573745f6672616d650000050000006a706567000000000a000000ffd8ffe000104a464946", "raw_data_size": 54, "timestamp": 1749315328603302144, "topic": "/test/sensor_msgs/compressed_image"}, {"connection_id": 39, "deserialized_data": "sensor_msgs__msg__FluidPressure(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=949492480, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), fluid_pressure=101325.0, variance=100.0, __msgtype__='sensor_msgs/msg/FluidPressure')", "msgtype": "sensor_msgs/msg/FluidPressure", "raw_data_hex": "00010000fc6e4468001b98380b000000746573745f6672616d65000000000000d0bcf8400000000000005940", "raw_data_size": 44, "timestamp": 1749315328703302144, "topic": "/test/sensor_msgs/fluid_pressure"}, {"connection_id": 40, "deserialized_data": "sensor_msgs__msg__Illuminance(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=950534912, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), illuminance=500.0, variance=10.0, __msgtype__='sensor_msgs/msg/Illuminance')", "msgtype": "sensor_msgs/msg/Illuminance", "raw_data_hex": "00010000fc6e44680003a8380b000000746573745f6672616d6500000000000000407f400000000000002440", "raw_data_size": 44, "timestamp": 1749315328803302144, "topic": "/test/sensor_msgs/illuminance"}, {"connection_id": 41, "deserialized_data": "sensor_msgs__msg__Image(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=951608064, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), height=480, width=640, encoding='rgb8', is_bigendian=0, step=1920, data=array([255,   0,   0, 255,   0,   0, 255,   0,   0, 255,   0,   0, 255,\n         0,   0, 255,   0,   0, 255,   0,   0, 255,   0,   0, 255,   0,\n         0, 255,   0,   0], dtype=uint8), __msgtype__='sensor_msgs/msg/Image')", "msgtype": "sensor_msgs/msg/Image", "raw_data_hex": "00010000fc6e44680063b8380b000000746573745f6672616d650000e001000080020000050000007267623800000000800700001e000000ff0000ff0000ff0000ff0000ff0000ff0000ff0000ff0000ff0000ff0000", "raw_data_size": 86, "timestamp": 1749315328903302144, "topic": "/test/sensor_msgs/image"}, {"connection_id": 42, "deserialized_data": "sensor_msgs__msg__Imu(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=953324032, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), orientation_covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), angular_velocity=geometry_msgs__msg__Vector3(x=0.01, y=0.02, z=0.03, __msgtype__='geometry_msgs/msg/Vector3'), angular_velocity_covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01]), linear_acceleration=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), linear_acceleration_covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='sensor_msgs/msg/Imu')", "msgtype": "sensor_msgs/msg/Imu", "raw_data_hex": "00010000fc6e44680092d2380b000000746573745f6672616d650000000000000000000000000000000000000000000000000000000000000000f03f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f7b14ae47e17a843f7b14ae47e17a943fb81e85eb51b89e3f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f9a9999999999b93f9a9999999999c93f9a999999999923409a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 324, "timestamp": 1749315329003302144, "topic": "/test/sensor_msgs/imu"}, {"connection_id": 43, "deserialized_data": "sensor_msgs__msg__JointState(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=955078144, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), name=['joint1', 'joint2', 'joint3'], position=array([0.1, 0.2, 0.3]), velocity=array([0.01, 0.02, 0.03]), effort=array([1., 2., 3.]), __msgtype__='sensor_msgs/msg/JointState')", "msgtype": "sensor_msgs/msg/JointState", "raw_data_hex": "00010000fc6e44680056ed380b000000746573745f6672616d65000003000000070000006a6f696e74310000070000006a6f696e74320000070000006a6f696e7433000003000000000000009a9999999999b93f9a9999999999c93f333333333333d33f03000000000000007b14ae47e17a843f7b14ae47e17a943fb81e85eb51b89e3f0300000000000000000000000000f03f00000000000000400000000000000840", "raw_data_size": 164, "timestamp": 1749315329103302144, "topic": "/test/sensor_msgs/joint_state"}, {"connection_id": 44, "deserialized_data": "sensor_msgs__msg__Joy(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=957387520, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), axes=array([ 0. ,  0.5, -0.3,  1. ], dtype=float32), buttons=array([0, 1, 0, 1, 0], dtype=int32), __msgtype__='sensor_msgs/msg/Joy')", "msgtype": "sensor_msgs/msg/Joy", "raw_data_hex": "00010000fc6e4468009310390b000000746573745f6672616d65000004000000000000000000003f9a9999be0000803f050000000000000001000000000000000100000000000000", "raw_data_size": 72, "timestamp": 1749315329203302144, "topic": "/test/sensor_msgs/joy"}, {"connection_id": 45, "deserialized_data": "sensor_msgs__msg__JoyFeedback(type=1, id=0, intensity=0.800000011920929, TYPE_LED=0, TYPE_RUMBLE=1, TYPE_BUZZER=2, __msgtype__='sensor_msgs/msg/JoyFeedback')", "msgtype": "sensor_msgs/msg/JoyFeedback", "raw_data_hex": "0001000001000000cdcc4c3f", "raw_data_size": 12, "timestamp": 1749315329303302144, "topic": "/test/sensor_msgs/joy_feedback"}, {"connection_id": 46, "deserialized_data": "sensor_msgs__msg__JoyFeedbackArray(array=[sensor_msgs__msg__JoyFeedback(type=1, id=0, intensity=0.800000011920929, TYPE_LED=0, TYPE_RUMBLE=1, TYPE_BUZZER=2, __msgtype__='sensor_msgs/msg/JoyFeedback'), sensor_msgs__msg__JoyFeedback(type=2, id=1, intensity=0.5, TYPE_LED=0, TYPE_RUMBLE=1, TYPE_BUZZER=2, __msgtype__='sensor_msgs/msg/JoyFeedback')], __msgtype__='sensor_msgs/msg/JoyFeedbackArray')", "msgtype": "sensor_msgs/msg/JoyFeedbackArray", "raw_data_hex": "000100000200000001000000cdcc4c3f020100000000003f", "raw_data_size": 24, "timestamp": 1749315329403302144, "topic": "/test/sensor_msgs/joy_feedback_array"}, {"connection_id": 47, "deserialized_data": "sensor_msgs__msg__LaserEcho(echoes=array([1.5, 2. , 2.5], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho')", "msgtype": "sensor_msgs/msg/LaserEcho", "raw_data_hex": "00010000030000000000c03f0000004000002040", "raw_data_size": 20, "timestamp": 1749315329503302144, "topic": "/test/sensor_msgs/laser_echo"}, {"connection_id": 48, "deserialized_data": "sensor_msgs__msg__LaserScan(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=962061056, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), angle_min=-1.5700000524520874, angle_max=1.5700000524520874, angle_increment=0.009999999776482582, time_increment=0.0, scan_time=0.10000000149011612, range_min=0.10000000149011612, range_max=10.0, ranges=array([1., 2., 3., 4., 5.], dtype=float32), intensities=array([100., 200., 300., 400., 500.], dtype=float32), __msgtype__='sensor_msgs/msg/LaserScan')", "msgtype": "sensor_msgs/msg/LaserScan", "raw_data_hex": "00010000fc6e446800e357390b000000746573745f6672616d650000c3f5c8bfc3f5c83f0ad7233c00000000cdcccc3dcdcccc3d00002041050000000000803f0000004000004040000080400000a040050000000000c84200004843000096430000c8430000fa43", "raw_data_size": 104, "timestamp": 1749315329603302144, "topic": "/test/sensor_msgs/laser_scan"}, {"connection_id": 49, "deserialized_data": "sensor_msgs__msg__MagneticField(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=964027136, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), magnetic_field=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3, __msgtype__='geometry_msgs/msg/Vector3'), magnetic_field_covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01]), __msgtype__='sensor_msgs/msg/MagneticField')", "msgtype": "sensor_msgs/msg/MagneticField", "raw_data_hex": "00010000fc6e446800e375390b000000746573745f6672616d6500009a9999999999b93f9a9999999999c93f333333333333d33f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f", "raw_data_size": 124, "timestamp": 1749315329703302144, "topic": "/test/sensor_msgs/magnetic_field"}, {"connection_id": 50, "deserialized_data": "sensor_msgs__msg__MultiDOFJointState(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=965208320, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), joint_names=['joint1', 'joint2'], transforms=[geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform'), geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=0.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform')], twist=[geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.01, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=0.0, y=0.1, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.01, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist')], wrench=[geometry_msgs__msg__Wrench(force=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), torque=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Wrench'), geometry_msgs__msg__Wrench(force=geometry_msgs__msg__Vector3(x=0.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), torque=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Wrench')], __msgtype__='sensor_msgs/msg/MultiDOFJointState')", "msgtype": "sensor_msgs/msg/MultiDOFJointState", "raw_data_hex": "00010000fc6e446800e987390b000000746573745f6672616d65000002000000070000006a6f696e74310000070000006a6f696e7432000002000000000000000000f03f00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03f0000000000000000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000f03f02000000000000009a9999999999b93f00000000000000000000000000000000000000000000000000000000000000007b14ae47e17a843f00000000000000009a9999999999b93f0000000000000000000000000000000000000000000000007b14ae47e17a843f0200000000000000000000000000f03f00000000000000000000000000000000000000000000000000000000000000009a9999999999b93f0000000000000000000000000000f03f0000000000000000000000000000000000000000000000009a9999999999b93f", "raw_data_size": 380, "timestamp": 1749315329803302144, "topic": "/test/sensor_msgs/multi_dof_joint_state"}, {"connection_id": 51, "deserialized_data": "sensor_msgs__msg__MultiEchoLaserScan(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=967619840, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), angle_min=-1.5700000524520874, angle_max=1.5700000524520874, angle_increment=0.10000000149011612, time_increment=0.0010000000474974513, scan_time=0.10000000149011612, range_min=0.10000000149011612, range_max=10.0, ranges=[sensor_msgs__msg__LaserEcho(echoes=array([1.5, 2. ], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho'), sensor_msgs__msg__LaserEcho(echoes=array([2.5, 3. ], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho'), sensor_msgs__msg__LaserEcho(echoes=array([3.5, 4. ], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho')], intensities=[sensor_msgs__msg__LaserEcho(echoes=array([100., 150.], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho'), sensor_msgs__msg__LaserEcho(echoes=array([200., 250.], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho'), sensor_msgs__msg__LaserEcho(echoes=array([300., 350.], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho')], __msgtype__='sensor_msgs/msg/MultiEchoLaserScan')", "msgtype": "sensor_msgs/msg/MultiEchoLaserScan", "raw_data_hex": "00010000fc6e446800b5ac390b000000746573745f6672616d650000c3f5c8bfc3f5c83fcdcccc3d6f12833acdcccc3dcdcccc3d0000204103000000020000000000c03f0000004002000000000020400000404002000000000060400000804003000000020000000000c84200001643020000000000484300007a4302000000000096430000af43", "raw_data_size": 136, "timestamp": 1749315329903302144, "topic": "/test/sensor_msgs/multi_echo_laser_scan"}, {"connection_id": 52, "deserialized_data": "sensor_msgs__msg__NavSatFix(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=969792768, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), status=sensor_msgs__msg__NavSatStatus(status=0, service=1, STATUS_NO_FIX=-1, STATUS_FIX=0, STATUS_SBAS_FIX=1, STATUS_GBAS_FIX=2, SERVICE_GPS=1, SERVICE_GLONASS=2, SERVICE_COMPASS=4, SERVICE_GALILEO=8, __msgtype__='sensor_msgs/msg/NavSatStatus'), latitude=37.7749, longitude=-122.4194, altitude=100.0, position_covariance=array([1., 1., 1., 1., 1., 1., 1., 1., 1.]), position_covariance_type=1, COVARIANCE_TYPE_UNKNOWN=0, COVARIANCE_TYPE_APPROXIMATED=1, COVARIANCE_TYPE_DIAGONAL_KNOWN=2, COVARIANCE_TYPE_KNOWN=3, __msgtype__='sensor_msgs/msg/NavSatFix')", "msgtype": "sensor_msgs/msg/NavSatFix", "raw_data_hex": "00010000fc6e446800ddcd390b000000746573745f6672616d6500000100000000000000d0d556ec2fe3424050fc1873d79a5ec00000000000005940000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f01", "raw_data_size": 133, "timestamp": 1749315330003302144, "topic": "/test/sensor_msgs/nav_sat_fix"}, {"connection_id": 53, "deserialized_data": "sensor_msgs__msg__NavSatStatus(status=0, service=1, STATUS_NO_FIX=-1, STATUS_FIX=0, STATUS_SBAS_FIX=1, STATUS_GBAS_FIX=2, SERVICE_GPS=1, SERVICE_GLONASS=2, SERVICE_COMPASS=4, SERVICE_GALILEO=8, __msgtype__='sensor_msgs/msg/NavSatStatus')", "msgtype": "sensor_msgs/msg/NavSatStatus", "raw_data_hex": "0001000000000100", "raw_data_size": 8, "timestamp": 1749315330103302144, "topic": "/test/sensor_msgs/nav_sat_status"}, {"connection_id": 54, "deserialized_data": "sensor_msgs__msg__PointCloud(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=972137472, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), points=[geometry_msgs__msg__Point32(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=4.0, y=5.0, z=6.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=7.0, y=8.0, z=9.0, __msgtype__='geometry_msgs/msg/Point32')], channels=[sensor_msgs__msg__ChannelFloat32(name='intensity', values=array([100., 200., 300.], dtype=float32), __msgtype__='sensor_msgs/msg/ChannelFloat32')], __msgtype__='sensor_msgs/msg/PointCloud')", "msgtype": "sensor_msgs/msg/PointCloud", "raw_data_hex": "00010000fc6e446800a4f1390b000000746573745f6672616d650000030000000000803f0000004000004040000080400000a0400000c0400000e0400000004100001041010000000a000000696e74656e73697479000000030000000000c8420000484300009643", "raw_data_size": 104, "timestamp": 1749315330203302144, "topic": "/test/sensor_msgs/point_cloud"}, {"connection_id": 55, "deserialized_data": "sensor_msgs__msg__PointCloud2(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=973697024, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), height=1, width=3, fields=[sensor_msgs__msg__PointField(name='x', offset=0, datatype=7, count=1, INT8=1, UINT8=2, INT16=3, UINT16=4, INT32=5, UINT32=6, FLOAT32=7, FLOAT64=8, __msgtype__='sensor_msgs/msg/PointField'), sensor_msgs__msg__PointField(name='y', offset=4, datatype=7, count=1, INT8=1, UINT8=2, INT16=3, UINT16=4, INT32=5, UINT32=6, FLOAT32=7, FLOAT64=8, __msgtype__='sensor_msgs/msg/PointField'), sensor_msgs__msg__PointField(name='z', offset=8, datatype=7, count=1, INT8=1, UINT8=2, INT16=3, UINT16=4, INT32=5, UINT32=6, FLOAT32=7, FLOAT64=8, __msgtype__='sensor_msgs/msg/PointField')], is_bigendian=False, point_step=12, row_step=36, data=array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], dtype=uint8), is_dense=True, __msgtype__='sensor_msgs/msg/PointCloud2')", "msgtype": "sensor_msgs/msg/PointCloud2", "raw_data_hex": "00010000fc6e44680070093a0b000000746573745f6672616d65000001000000030000000300000002000000780000000000000007000000010000000200000079000000040000000700000001000000020000007a000000080000000700000001000000000000000c000000240000002400000000000000000000000000000000000000000000000000000000000000000000000000000001", "raw_data_size": 153, "timestamp": 1749315330303302144, "topic": "/test/sensor_msgs/point_cloud2"}, {"connection_id": 56, "deserialized_data": "sensor_msgs__msg__PointField(name='x', offset=0, datatype=7, count=1, INT8=1, UINT8=2, INT16=3, UINT16=4, INT32=5, UINT32=6, FLOAT32=7, FLOAT64=8, __msgtype__='sensor_msgs/msg/PointField')", "msgtype": "sensor_msgs/msg/PointField", "raw_data_hex": "000100000200000078000000000000000700000001000000", "raw_data_size": 24, "timestamp": 1749315330403302144, "topic": "/test/sensor_msgs/point_field"}, {"connection_id": 57, "deserialized_data": "sensor_msgs__msg__Range(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=976997632, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), radiation_type=0, field_of_view=0.10000000149011612, min_range=0.019999999552965164, max_range=2.0, range=1.5, ULTRASOUND=0, INFRARED=1, __msgtype__='sensor_msgs/msg/Range')", "msgtype": "sensor_msgs/msg/Range", "raw_data_hex": "00010000fc6e446800cd3b3a0b000000746573745f6672616d650000cdcccc3d0ad7a33c000000400000c03f", "raw_data_size": 44, "timestamp": 1749315330503302144, "topic": "/test/sensor_msgs/range"}, {"connection_id": 58, "deserialized_data": "sensor_msgs__msg__RegionOfInterest(x_offset=10, y_offset=20, height=100, width=200, do_rectify=False, __msgtype__='sensor_msgs/msg/RegionOfInterest')", "msgtype": "sensor_msgs/msg/RegionOfInterest", "raw_data_hex": "000100000a0000001400000064000000c800000000", "raw_data_size": 21, "timestamp": 1749315330603302144, "topic": "/test/sensor_msgs/region_of_interest"}, {"connection_id": 59, "deserialized_data": "sensor_msgs__msg__RelativeHumidity(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=978241024, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), relative_humidity=0.65, variance=0.01, __msgtype__='sensor_msgs/msg/RelativeHumidity')", "msgtype": "sensor_msgs/msg/RelativeHumidity", "raw_data_hex": "00010000fc6e446800c64e3a0b000000746573745f6672616d650000cdcccccccccce43f7b14ae47e17a843f", "raw_data_size": 44, "timestamp": 1749315330703302144, "topic": "/test/sensor_msgs/relative_humidity"}, {"connection_id": 60, "deserialized_data": "sensor_msgs__msg__Temperature(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=979424000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), temperature=25.5, variance=0.1, __msgtype__='sensor_msgs/msg/Temperature')", "msgtype": "sensor_msgs/msg/Temperature", "raw_data_hex": "00010000fc6e446800d3603a0b000000746573745f6672616d65000000000000008039409a9999999999b93f", "raw_data_size": 44, "timestamp": 1749315330803302144, "topic": "/test/sensor_msgs/temperature"}, {"connection_id": 61, "deserialized_data": "sensor_msgs__msg__TimeReference(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=980608000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), time_ref=builtin_interfaces__msg__Time(sec=1234567890, nanosec=123456789, __msgtype__='builtin_interfaces/msg/Time'), source='gps', __msgtype__='sensor_msgs/msg/TimeReference')", "msgtype": "sensor_msgs/msg/TimeReference", "raw_data_hex": "00010000fc6e446800e4723a0b000000746573745f6672616d650000d202964915cd5b070400000067707300", "raw_data_size": 44, "timestamp": 1749315330903302144, "topic": "/test/sensor_msgs/time_reference"}, {"connection_id": 62, "deserialized_data": "std_msgs__msg__Bool(data=True, __msgtype__='std_msgs/msg/Bool')", "msgtype": "std_msgs/msg/Bool", "raw_data_hex": "0001000001", "raw_data_size": 5, "timestamp": 1749315331003302144, "topic": "/test/std_msgs/bool"}, {"connection_id": 63, "deserialized_data": "std_msgs__msg__Byte(data=42, __msgtype__='std_msgs/msg/Byte')", "msgtype": "std_msgs/msg/Byte", "raw_data_hex": "000100002a", "raw_data_size": 5, "timestamp": 1749315331103302144, "topic": "/test/std_msgs/byte"}, {"connection_id": 64, "deserialized_data": "std_msgs__msg__ByteMultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([65, 66, 67], dtype=int8), __msgtype__='std_msgs/msg/ByteMultiArray')", "msgtype": "std_msgs/msg/ByteMultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000414243", "raw_data_size": 35, "timestamp": 1749315331203302144, "topic": "/test/std_msgs/byte_multi_array"}, {"connection_id": 65, "deserialized_data": "std_msgs__msg__Char(data=65, __msgtype__='std_msgs/msg/Char')", "msgtype": "std_msgs/msg/Char", "raw_data_hex": "0001000041", "raw_data_size": 5, "timestamp": 1749315331303302144, "topic": "/test/std_msgs/char"}, {"connection_id": 66, "deserialized_data": "std_msgs__msg__ColorRGBA(r=1.0, g=0.5, b=0.0, a=0.800000011920929, __msgtype__='std_msgs/msg/ColorRGBA')", "msgtype": "std_msgs/msg/ColorRGBA", "raw_data_hex": "000100000000803f0000003f00000000cdcc4c3f", "raw_data_size": 20, "timestamp": 1749315331403302144, "topic": "/test/std_msgs/color_rgba"}, {"connection_id": 67, "deserialized_data": "std_msgs__msg__Empty(structure_needs_at_least_one_member=0, __msgtype__='std_msgs/msg/Empty')", "msgtype": "std_msgs/msg/Empty", "raw_data_hex": "0001000000", "raw_data_size": 5, "timestamp": 1749315331503302144, "topic": "/test/std_msgs/empty"}, {"connection_id": 68, "deserialized_data": "std_msgs__msg__Float32(data=3.141590118408203, __msgtype__='std_msgs/msg/Float32')", "msgtype": "std_msgs/msg/Float32", "raw_data_hex": "00010000d00f4940", "raw_data_size": 8, "timestamp": 1749315331603302144, "topic": "/test/std_msgs/float32"}, {"connection_id": 69, "deserialized_data": "std_msgs__msg__Float32MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1.1, 2.2, 3.3], dtype=float32), __msgtype__='std_msgs/msg/Float32MultiArray')", "msgtype": "std_msgs/msg/Float32MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000cdcc8c3fcdcc0c4033335340", "raw_data_size": 44, "timestamp": 1749315331703302144, "topic": "/test/std_msgs/float32_multi_array"}, {"connection_id": 70, "deserialized_data": "std_msgs__msg__Float64(data=2.71828, __msgtype__='std_msgs/msg/Float64')", "msgtype": "std_msgs/msg/Float64", "raw_data_hex": "0001000090f7aa9509bf0540", "raw_data_size": 12, "timestamp": 1749315331803302144, "topic": "/test/std_msgs/float64"}, {"connection_id": 71, "deserialized_data": "std_msgs__msg__Float64MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1.11, 2.22, 3.33]), __msgtype__='std_msgs/msg/Float64MultiArray')", "msgtype": "std_msgs/msg/Float64MultiArray", "raw_data_hex": "000100000100000002000000780000000300000003000000000000000300000000000000c3f5285c8fc2f13fc3f5285c8fc20140a4703d0ad7a30a40", "raw_data_size": 60, "timestamp": 1749315331903302144, "topic": "/test/std_msgs/float64_multi_array"}, {"connection_id": 72, "deserialized_data": "std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=992510464, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header')", "msgtype": "std_msgs/msg/Header", "raw_data_hex": "00010000fc6e44680082283b0b000000746573745f6672616d6500", "raw_data_size": 27, "timestamp": 1749315332003302144, "topic": "/test/std_msgs/header"}, {"connection_id": 73, "deserialized_data": "std_msgs__msg__Int16(data=-1000, __msgtype__='std_msgs/msg/Int16')", "msgtype": "std_msgs/msg/Int16", "raw_data_hex": "0001000018fc", "raw_data_size": 6, "timestamp": 1749315332103302144, "topic": "/test/std_msgs/int16"}, {"connection_id": 74, "deserialized_data": "std_msgs__msg__Int16MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([10, 20, 30], dtype=int16), __msgtype__='std_msgs/msg/Int16MultiArray')", "msgtype": "std_msgs/msg/Int16MultiArray", "raw_data_hex": "00010000010000000200000078000000030000000300000000000000030000000a0014001e00", "raw_data_size": 38, "timestamp": 1749315332203302144, "topic": "/test/std_msgs/int16_multi_array"}, {"connection_id": 75, "deserialized_data": "std_msgs__msg__Int32(data=-100000, __msgtype__='std_msgs/msg/Int32')", "msgtype": "std_msgs/msg/Int32", "raw_data_hex": "000100006079feff", "raw_data_size": 8, "timestamp": 1749315332303302144, "topic": "/test/std_msgs/int32"}, {"connection_id": 76, "deserialized_data": "std_msgs__msg__Int32MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1, 2, 3], dtype=int32), __msgtype__='std_msgs/msg/Int32MultiArray')", "msgtype": "std_msgs/msg/Int32MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000010000000200000003000000", "raw_data_size": 44, "timestamp": 1749315332403302144, "topic": "/test/std_msgs/int32_multi_array"}, {"connection_id": 77, "deserialized_data": "std_msgs__msg__Int64(data=-10000000000, __msgtype__='std_msgs/msg/Int64')", "msgtype": "std_msgs/msg/Int64", "raw_data_hex": "00010000001cf4abfdffffff", "raw_data_size": 12, "timestamp": 1749315332503302144, "topic": "/test/std_msgs/int64"}, {"connection_id": 78, "deserialized_data": "std_msgs__msg__Int64MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([100, 200, 300]), __msgtype__='std_msgs/msg/Int64MultiArray')", "msgtype": "std_msgs/msg/Int64MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000000000006400000000000000c8000000000000002c01000000000000", "raw_data_size": 60, "timestamp": 1749315332603302144, "topic": "/test/std_msgs/int64_multi_array"}, {"connection_id": 79, "deserialized_data": "std_msgs__msg__Int8(data=-42, __msgtype__='std_msgs/msg/Int8')", "msgtype": "std_msgs/msg/Int8", "raw_data_hex": "00010000d6", "raw_data_size": 5, "timestamp": 1749315332703302144, "topic": "/test/std_msgs/int8"}, {"connection_id": 80, "deserialized_data": "std_msgs__msg__Int8MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1, 2, 3], dtype=int8), __msgtype__='std_msgs/msg/Int8MultiArray')", "msgtype": "std_msgs/msg/Int8MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000010203", "raw_data_size": 35, "timestamp": 1749315332803302144, "topic": "/test/std_msgs/int8_multi_array"}, {"connection_id": 81, "deserialized_data": "std_msgs__msg__MultiArrayDimension(label='dimension', size=10, stride=10, __msgtype__='std_msgs/msg/MultiArrayDimension')", "msgtype": "std_msgs/msg/MultiArrayDimension", "raw_data_hex": "000100000a00000064696d656e73696f6e0000000a0000000a000000", "raw_data_size": 28, "timestamp": 1749315332903302144, "topic": "/test/std_msgs/multi_array_dimension"}, {"connection_id": 82, "deserialized_data": "std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout')", "msgtype": "std_msgs/msg/MultiArrayLayout", "raw_data_hex": "00010000010000000200000078000000030000000300000000000000", "raw_data_size": 28, "timestamp": 1749315333003302144, "topic": "/test/std_msgs/multi_array_layout"}, {"connection_id": 83, "deserialized_data": "std_msgs__msg__String(data='Hello, ROS2!', __msgtype__='std_msgs/msg/String')", "msgtype": "std_msgs/msg/String", "raw_data_hex": "000100000d00000048656c6c6f2c20524f53322100", "raw_data_size": 21, "timestamp": 1749315333103302144, "topic": "/test/std_msgs/string"}, {"connection_id": 84, "deserialized_data": "std_msgs__msg__UInt16(data=65535, __msgtype__='std_msgs/msg/UInt16')", "msgtype": "std_msgs/msg/UInt16", "raw_data_hex": "00010000ffff", "raw_data_size": 6, "timestamp": 1749315333203302144, "topic": "/test/std_msgs/uint16"}, {"connection_id": 85, "deserialized_data": "std_msgs__msg__UInt16MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([10, 20, 30], dtype=uint16), __msgtype__='std_msgs/msg/UInt16MultiArray')", "msgtype": "std_msgs/msg/UInt16MultiArray", "raw_data_hex": "00010000010000000200000078000000030000000300000000000000030000000a0014001e00", "raw_data_size": 38, "timestamp": 1749315333303302144, "topic": "/test/std_msgs/uint16_multi_array"}, {"connection_id": 86, "deserialized_data": "std_msgs__msg__UInt32(data=4294967295, __msgtype__='std_msgs/msg/UInt32')", "msgtype": "std_msgs/msg/UInt32", "raw_data_hex": "00010000ffffffff", "raw_data_size": 8, "timestamp": 1749315333403302144, "topic": "/test/std_msgs/uint32"}, {"connection_id": 87, "deserialized_data": "std_msgs__msg__UInt32MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([100, 200, 300], dtype=uint32), __msgtype__='std_msgs/msg/UInt32MultiArray')", "msgtype": "std_msgs/msg/UInt32MultiArray", "raw_data_hex": "000100000100000002000000780000000300000003000000000000000300000064000000c80000002c010000", "raw_data_size": 44, "timestamp": 1749315333503302144, "topic": "/test/std_msgs/uint32_multi_array"}, {"connection_id": 88, "deserialized_data": "std_msgs__msg__UInt64(data=18446744073709551615, __msgtype__='std_msgs/msg/UInt64')", "msgtype": "std_msgs/msg/UInt64", "raw_data_hex": "00010000ffffffffffffffff", "raw_data_size": 12, "timestamp": 1749315333603302144, "topic": "/test/std_msgs/uint64"}, {"connection_id": 89, "deserialized_data": "std_msgs__msg__UInt64MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1000, 2000, 3000], dtype=uint64), __msgtype__='std_msgs/msg/UInt64MultiArray')", "msgtype": "std_msgs/msg/UInt64MultiArray", "raw_data_hex": "000100000100000002000000780000000300000003000000000000000300000000000000e803000000000000d007000000000000b80b000000000000", "raw_data_size": 60, "timestamp": 1749315333703302144, "topic": "/test/std_msgs/uint64_multi_array"}, {"connection_id": 90, "deserialized_data": "std_msgs__msg__UInt8(data=255, __msgtype__='std_msgs/msg/UInt8')", "msgtype": "std_msgs/msg/UInt8", "raw_data_hex": "00010000ff", "raw_data_size": 5, "timestamp": 1749315333803302144, "topic": "/test/std_msgs/uint8"}, {"connection_id": 91, "deserialized_data": "std_msgs__msg__UInt8MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1, 2, 3], dtype=uint8), __msgtype__='std_msgs/msg/UInt8MultiArray')", "msgtype": "std_msgs/msg/UInt8MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000010203", "raw_data_size": 35, "timestamp": 1749315333903302144, "topic": "/test/std_msgs/uint8_multi_array"}, {"connection_id": 92, "deserialized_data": "stereo_msgs__msg__DisparityImage(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=8656896, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), image=sensor_msgs__msg__Image(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=8660736, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), height=480, width=640, encoding='32FC1', is_bigendian=0, step=2560, data=array([  0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63], dtype=uint8), __msgtype__='sensor_msgs/msg/Image'), f=525.0, t=0.10000000149011612, valid_window=sensor_msgs__msg__RegionOfInterest(x_offset=0, y_offset=0, height=480, width=640, do_rectify=False, __msgtype__='sensor_msgs/msg/RegionOfInterest'), min_disparity=0.0, max_disparity=64.0, delta_d=0.125, __msgtype__='stereo_msgs/msg/DisparityImage')", "msgtype": "stereo_msgs/msg/DisparityImage", "raw_data_hex": "00010000fd6e4468001884000b000000746573745f6672616d650000fd6e4468002784000b000000746573745f6672616d650000e001000080020000060000003332464331000000000a0000900100000000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f00400344cdcccc3d0000000000000000e0010000800200000000000000000000000080420000003e", "raw_data_size": 520, "timestamp": 1749315334003302144, "topic": "/test/stereo_msgs/disparity_image"}, {"connection_id": 93, "deserialized_data": "tf2_msgs__msg__TF2Error(error=0, error_string='No error', NO_ERROR=0, LOOKUP_ERROR=1, CONNECTIVITY_ERROR=2, EXTRAPOLATION_ERROR=3, INVALID_ARGUMENT_ERROR=4, TIMEOUT_ERROR=5, TRANSFORM_ERROR=6, __msgtype__='tf2_msgs/msg/TF2Error')", "msgtype": "tf2_msgs/msg/TF2Error", "raw_data_hex": "0001000000000000090000004e6f206572726f7200", "raw_data_size": 21, "timestamp": 1749315334103302144, "topic": "/test/tf2_msgs/tf2_error"}, {"connection_id": 94, "deserialized_data": "tf2_msgs__msg__TFMessage(transforms=[geometry_msgs__msg__TransformStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=11293440, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), child_frame_id='child_frame', transform=geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform'), __msgtype__='geometry_msgs/msg/TransformStamped')], __msgtype__='tf2_msgs/msg/TFMessage')", "msgtype": "tf2_msgs/msg/TFMessage", "raw_data_hex": "0001000001000000fd6e44680053ac000b000000746573745f6672616d6500000c0000006368696c645f6672616d650000000000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 108, "timestamp": 1749315334203302144, "topic": "/test/tf2_msgs/tf_message"}, {"connection_id": 1, "deserialized_data": "geometry_msgs__msg__Accel(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Accel')", "msgtype": "geometry_msgs/msg/Accel", "raw_data_hex": "000100009a9999999999b93f9a9999999999c93f9a99999999992340000000000000000000000000000000009a9999999999b93f", "raw_data_size": 52, "timestamp": 1749315334303302144, "topic": "/test/geometry_msgs/accel"}, {"connection_id": 2, "deserialized_data": "geometry_msgs__msg__AccelStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=12847872, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), accel=geometry_msgs__msg__Accel(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Accel'), __msgtype__='geometry_msgs/msg/AccelStamped')", "msgtype": "geometry_msgs/msg/AccelStamped", "raw_data_hex": "00010000fd6e4468000bc4000b000000746573745f6672616d6500009a9999999999b93f9a9999999999c93f9a99999999992340000000000000000000000000000000009a9999999999b93f", "raw_data_size": 76, "timestamp": 1749315334403302144, "topic": "/test/geometry_msgs/accel_stamped"}, {"connection_id": 3, "deserialized_data": "geometry_msgs__msg__AccelWithCovariance(accel=geometry_msgs__msg__Accel(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Accel'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/AccelWithCovariance')", "msgtype": "geometry_msgs/msg/AccelWithCovariance", "raw_data_hex": "000100009a9999999999b93f9a9999999999c93f9a99999999992340000000000000000000000000000000009a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 340, "timestamp": 1749315334503302144, "topic": "/test/geometry_msgs/accel_with_covariance"}, {"connection_id": 4, "deserialized_data": "geometry_msgs__msg__AccelWithCovarianceStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=12890112, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), accel=geometry_msgs__msg__AccelWithCovariance(accel=geometry_msgs__msg__Accel(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Accel'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/AccelWithCovariance'), __msgtype__='geometry_msgs/msg/AccelWithCovarianceStamped')", "msgtype": "geometry_msgs/msg/AccelWithCovarianceStamped", "raw_data_hex": "00010000fd6e446800b0c4000b000000746573745f6672616d6500009a9999999999b93f9a9999999999c93f9a99999999992340000000000000000000000000000000009a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 364, "timestamp": 1749315334603302144, "topic": "/test/geometry_msgs/accel_with_covariance_stamped"}, {"connection_id": 5, "deserialized_data": "geometry_msgs__msg__Inertia(m=1.5, com=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), ixx=0.1, ixy=0.0, ixz=0.0, iyy=0.1, iyz=0.0, izz=0.1, __msgtype__='geometry_msgs/msg/Inertia')", "msgtype": "geometry_msgs/msg/Inertia", "raw_data_hex": "00010000000000000000f83f0000000000000000000000000000000000000000000000009a9999999999b93f000000000000000000000000000000009a9999999999b93f00000000000000009a9999999999b93f", "raw_data_size": 84, "timestamp": 1749315334703302144, "topic": "/test/geometry_msgs/inertia"}, {"connection_id": 6, "deserialized_data": "geometry_msgs__msg__InertiaStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=12925184, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), inertia=geometry_msgs__msg__Inertia(m=1.5, com=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), ixx=0.1, ixy=0.0, ixz=0.0, iyy=0.1, iyz=0.0, izz=0.1, __msgtype__='geometry_msgs/msg/Inertia'), __msgtype__='geometry_msgs/msg/InertiaStamped')", "msgtype": "geometry_msgs/msg/InertiaStamped", "raw_data_hex": "00010000fd6e44680039c5000b000000746573745f6672616d650000000000000000f83f0000000000000000000000000000000000000000000000009a9999999999b93f000000000000000000000000000000009a9999999999b93f00000000000000009a9999999999b93f", "raw_data_size": 108, "timestamp": 1749315334803302144, "topic": "/test/geometry_msgs/inertia_stamped"}, {"connection_id": 7, "deserialized_data": "geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point')", "msgtype": "geometry_msgs/msg/Point", "raw_data_hex": "00010000000000000000f03f00000000000000400000000000000840", "raw_data_size": 28, "timestamp": 1749315334903302144, "topic": "/test/geometry_msgs/point"}, {"connection_id": 8, "deserialized_data": "geometry_msgs__msg__Point32(x=1.5, y=2.5, z=3.5, __msgtype__='geometry_msgs/msg/Point32')", "msgtype": "geometry_msgs/msg/Point32", "raw_data_hex": "000100000000c03f0000204000006040", "raw_data_size": 16, "timestamp": 1749315335003302144, "topic": "/test/geometry_msgs/point32"}, {"connection_id": 9, "deserialized_data": "geometry_msgs__msg__PointStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=12958976, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), point=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), __msgtype__='geometry_msgs/msg/PointStamped')", "msgtype": "geometry_msgs/msg/PointStamped", "raw_data_hex": "00010000fd6e446800bdc5000b000000746573745f6672616d650000000000000000f03f00000000000000400000000000000840", "raw_data_size": 52, "timestamp": 1749315335103302144, "topic": "/test/geometry_msgs/point_stamped"}, {"connection_id": 10, "deserialized_data": "geometry_msgs__msg__Polygon(points=[geometry_msgs__msg__Point32(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=0.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32')], __msgtype__='geometry_msgs/msg/Polygon')", "msgtype": "geometry_msgs/msg/Polygon", "raw_data_hex": "00010000040000000000000000000000000000000000803f00000000000000000000803f0000803f00000000000000000000803f00000000", "raw_data_size": 56, "timestamp": 1749315335203302144, "topic": "/test/geometry_msgs/polygon"}, {"connection_id": 11, "deserialized_data": "geometry_msgs__msg__PolygonStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=12985600, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), polygon=geometry_msgs__msg__Polygon(points=[geometry_msgs__msg__Point32(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=0.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point32')], __msgtype__='geometry_msgs/msg/Polygon'), __msgtype__='geometry_msgs/msg/PolygonStamped')", "msgtype": "geometry_msgs/msg/PolygonStamped", "raw_data_hex": "00010000fd6e44680025c6000b000000746573745f6672616d650000040000000000000000000000000000000000803f00000000000000000000803f0000803f00000000000000000000803f00000000", "raw_data_size": 80, "timestamp": 1749315335303302144, "topic": "/test/geometry_msgs/polygon_stamped"}, {"connection_id": 12, "deserialized_data": "geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose')", "msgtype": "geometry_msgs/msg/Pose", "raw_data_hex": "00010000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 60, "timestamp": 1749315335403302144, "topic": "/test/geometry_msgs/pose"}, {"connection_id": 13, "deserialized_data": "geometry_msgs__msg__Pose2D(x=1.0, y=2.0, theta=0.5, __msgtype__='geometry_msgs/msg/Pose2D')", "msgtype": "geometry_msgs/msg/Pose2D", "raw_data_hex": "00010000000000000000f03f0000000000000040000000000000e03f", "raw_data_size": 28, "timestamp": 1749315335503302144, "topic": "/test/geometry_msgs/pose2d"}, {"connection_id": 14, "deserialized_data": "geometry_msgs__msg__PoseArray(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13027584, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), poses=[geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose')], __msgtype__='geometry_msgs/msg/PoseArray')", "msgtype": "geometry_msgs/msg/PoseArray", "raw_data_hex": "00010000fd6e446800c9c6000b000000746573745f6672616d6500000200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03f000000000000f03f000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 148, "timestamp": 1749315335603302144, "topic": "/test/geometry_msgs/pose_array"}, {"connection_id": 15, "deserialized_data": "geometry_msgs__msg__PoseStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13046272, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='geometry_msgs/msg/PoseStamped')", "msgtype": "geometry_msgs/msg/PoseStamped", "raw_data_hex": "00010000fd6e44680012c7000b000000746573745f6672616d650000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 84, "timestamp": 1749315335703302144, "topic": "/test/geometry_msgs/pose_stamped"}, {"connection_id": 16, "deserialized_data": "geometry_msgs__msg__PoseWithCovariance(pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/PoseWithCovariance')", "msgtype": "geometry_msgs/msg/PoseWithCovariance", "raw_data_hex": "00010000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 348, "timestamp": 1749315335803302144, "topic": "/test/geometry_msgs/pose_with_covariance"}, {"connection_id": 17, "deserialized_data": "geometry_msgs__msg__PoseWithCovarianceStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13077504, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), pose=geometry_msgs__msg__PoseWithCovariance(pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/PoseWithCovariance'), __msgtype__='geometry_msgs/msg/PoseWithCovarianceStamped')", "msgtype": "geometry_msgs/msg/PoseWithCovarianceStamped", "raw_data_hex": "00010000fd6e4468008cc7000b000000746573745f6672616d650000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 372, "timestamp": 1749315335903302144, "topic": "/test/geometry_msgs/pose_with_covariance_stamped"}, {"connection_id": 18, "deserialized_data": "geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion')", "msgtype": "geometry_msgs/msg/Quaternion", "raw_data_hex": "00010000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 36, "timestamp": 1749315336003302144, "topic": "/test/geometry_msgs/quaternion"}, {"connection_id": 19, "deserialized_data": "geometry_msgs__msg__QuaternionStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13105664, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), quaternion=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/QuaternionStamped')", "msgtype": "geometry_msgs/msg/QuaternionStamped", "raw_data_hex": "00010000fd6e446800fac7000b000000746573745f6672616d650000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 60, "timestamp": 1749315336103302144, "topic": "/test/geometry_msgs/quaternion_stamped"}, {"connection_id": 20, "deserialized_data": "geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform')", "msgtype": "geometry_msgs/msg/Transform", "raw_data_hex": "00010000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 60, "timestamp": 1749315336203302144, "topic": "/test/geometry_msgs/transform"}, {"connection_id": 21, "deserialized_data": "geometry_msgs__msg__TransformStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13130496, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), child_frame_id='child_frame', transform=geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform'), __msgtype__='geometry_msgs/msg/TransformStamped')", "msgtype": "geometry_msgs/msg/TransformStamped", "raw_data_hex": "00010000fd6e4468005bc8000b000000746573745f6672616d6500000c0000006368696c645f6672616d6500000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 100, "timestamp": 1749315336303302144, "topic": "/test/geometry_msgs/transform_stamped"}, {"connection_id": 22, "deserialized_data": "geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist')", "msgtype": "geometry_msgs/msg/Twist", "raw_data_hex": "00010000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000e03f", "raw_data_size": 52, "timestamp": 1749315336403302144, "topic": "/test/geometry_msgs/twist"}, {"connection_id": 23, "deserialized_data": "geometry_msgs__msg__TwistStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13156608, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), twist=geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), __msgtype__='geometry_msgs/msg/TwistStamped')", "msgtype": "geometry_msgs/msg/TwistStamped", "raw_data_hex": "00010000fd6e446800c1c8000b000000746573745f6672616d650000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000e03f", "raw_data_size": 76, "timestamp": 1749315336503302144, "topic": "/test/geometry_msgs/twist_stamped"}, {"connection_id": 24, "deserialized_data": "geometry_msgs__msg__TwistWithCovariance(twist=geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01]), __msgtype__='geometry_msgs/msg/TwistWithCovariance')", "msgtype": "geometry_msgs/msg/TwistWithCovariance", "raw_data_hex": "00010000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000e03f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f", "raw_data_size": 340, "timestamp": 1749315336603302144, "topic": "/test/geometry_msgs/twist_with_covariance"}, {"connection_id": 25, "deserialized_data": "geometry_msgs__msg__TwistWithCovarianceStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13185280, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), twist=geometry_msgs__msg__TwistWithCovariance(twist=geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.5, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01]), __msgtype__='geometry_msgs/msg/TwistWithCovariance'), __msgtype__='geometry_msgs/msg/TwistWithCovarianceStamped')", "msgtype": "geometry_msgs/msg/TwistWithCovarianceStamped", "raw_data_hex": "00010000fd6e44680031c9000b000000746573745f6672616d650000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000e03f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f", "raw_data_size": 364, "timestamp": 1749315336703302144, "topic": "/test/geometry_msgs/twist_with_covariance_stamped"}, {"connection_id": 26, "deserialized_data": "geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3, __msgtype__='geometry_msgs/msg/Vector3')", "msgtype": "geometry_msgs/msg/Vector3", "raw_data_hex": "000100009a9999999999b93f9a9999999999c93f333333333333d33f", "raw_data_size": 28, "timestamp": 1749315336803302144, "topic": "/test/geometry_msgs/vector3"}, {"connection_id": 27, "deserialized_data": "geometry_msgs__msg__Vector3Stamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13212928, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), vector=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Vector3Stamped')", "msgtype": "geometry_msgs/msg/Vector3Stamped", "raw_data_hex": "00010000fd6e4468009dc9000b000000746573745f6672616d650000000000000000f03f00000000000000400000000000000840", "raw_data_size": 52, "timestamp": 1749315336903302144, "topic": "/test/geometry_msgs/vector3_stamped"}, {"connection_id": 28, "deserialized_data": "geometry_msgs__msg__Wrench(force=geometry_msgs__msg__Vector3(x=10.0, y=5.0, z=2.0, __msgtype__='geometry_msgs/msg/Vector3'), torque=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Wrench')", "msgtype": "geometry_msgs/msg/Wrench", "raw_data_hex": "000100000000000000002440000000000000144000000000000000409a9999999999b93f9a9999999999c93f333333333333d33f", "raw_data_size": 52, "timestamp": 1749315337003302144, "topic": "/test/geometry_msgs/wrench"}, {"connection_id": 29, "deserialized_data": "geometry_msgs__msg__WrenchStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13249536, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), wrench=geometry_msgs__msg__Wrench(force=geometry_msgs__msg__Vector3(x=10.0, y=5.0, z=2.0, __msgtype__='geometry_msgs/msg/Vector3'), torque=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Wrench'), __msgtype__='geometry_msgs/msg/WrenchStamped')", "msgtype": "geometry_msgs/msg/WrenchStamped", "raw_data_hex": "00010000fd6e4468002cca000b000000746573745f6672616d6500000000000000002440000000000000144000000000000000409a9999999999b93f9a9999999999c93f333333333333d33f", "raw_data_size": 76, "timestamp": 1749315337103302144, "topic": "/test/geometry_msgs/wrench_stamped"}, {"connection_id": 30, "deserialized_data": "nav_msgs__msg__GridCells(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13269248, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), cell_width=0.10000000149011612, cell_height=0.10000000149011612, cells=[geometry_msgs__msg__Point(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), geometry_msgs__msg__Point(x=2.0, y=2.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), geometry_msgs__msg__Point(x=3.0, y=3.0, z=0.0, __msgtype__='geometry_msgs/msg/Point')], __msgtype__='nav_msgs/msg/GridCells')", "msgtype": "nav_msgs/msg/GridCells", "raw_data_hex": "00010000fd6e44680079ca000b000000746573745f6672616d650000cdcccc3dcdcccc3d0300000000000000000000000000f03f000000000000f03f0000000000000000000000000000004000000000000000400000000000000000000000000000084000000000000008400000000000000000", "raw_data_size": 116, "timestamp": 1749315337203302144, "topic": "/test/nav_msgs/grid_cells"}, {"connection_id": 31, "deserialized_data": "nav_msgs__msg__MapMetaData(map_load_time=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), resolution=0.05000000074505806, width=10, height=10, origin=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='nav_msgs/msg/MapMetaData')", "msgtype": "nav_msgs/msg/MapMetaData", "raw_data_hex": "000100000000000000000000cdcc4c3d0a0000000a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 84, "timestamp": 1749315337303302144, "topic": "/test/nav_msgs/map_metadata"}, {"connection_id": 32, "deserialized_data": "nav_msgs__msg__OccupancyGrid(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13307904, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), info=nav_msgs__msg__MapMetaData(map_load_time=builtin_interfaces__msg__Time(sec=0, nanosec=0, __msgtype__='builtin_interfaces/msg/Time'), resolution=0.05000000074505806, width=10, height=10, origin=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='nav_msgs/msg/MapMetaData'), data=array([-1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1,\n       -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1],\n      dtype=int8), __msgtype__='nav_msgs/msg/OccupancyGrid')", "msgtype": "nav_msgs/msg/OccupancyGrid", "raw_data_hex": "00010000fd6e44680010cb000b000000746573745f6672616d6500000000000000000000cdcc4c3d0a0000000a00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03f64000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "raw_data_size": 212, "timestamp": 1749315337403302144, "topic": "/test/nav_msgs/occupancy_grid"}, {"connection_id": 33, "deserialized_data": "nav_msgs__msg__Odometry(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13336320, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), child_frame_id='base_link', pose=geometry_msgs__msg__PoseWithCovariance(pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=2.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1,\n       0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='geometry_msgs/msg/PoseWithCovariance'), twist=geometry_msgs__msg__TwistWithCovariance(twist=geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01,\n       0.01, 0.01, 0.01]), __msgtype__='geometry_msgs/msg/TwistWithCovariance'), __msgtype__='nav_msgs/msg/Odometry')", "msgtype": "nav_msgs/msg/Odometry", "raw_data_hex": "00010000fd6e4468007fcb000b000000746573745f6672616d6500000a000000626173655f6c696e6b000000000000000000f03f00000000000000400000000000000000000000000000000000000000000000000000000000000000000000000000f03f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f000000000000f03f00000000000000000000000000000000000000000000000000000000000000009a9999999999b93f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f", "raw_data_size": 724, "timestamp": 1749315337503302144, "topic": "/test/nav_msgs/odometry"}, {"connection_id": 34, "deserialized_data": "nav_msgs__msg__Path(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13364224, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), poses=[geometry_msgs__msg__PoseStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13365760, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=0.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='geometry_msgs/msg/PoseStamped'), geometry_msgs__msg__PoseStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13367552, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), pose=geometry_msgs__msg__Pose(position=geometry_msgs__msg__Point(x=1.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Point'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Pose'), __msgtype__='geometry_msgs/msg/PoseStamped')], __msgtype__='nav_msgs/msg/Path')", "msgtype": "nav_msgs/msg/Path", "raw_data_hex": "00010000fd6e446800eccb000b000000746573745f6672616d65000002000000fd6e446800f2cb000b000000746573745f6672616d65000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03ffd6e446800f9cb000b000000746573745f6672616d650000000000000000f03f000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 196, "timestamp": 1749315337603302144, "topic": "/test/nav_msgs/path"}, {"connection_id": 35, "deserialized_data": "sensor_msgs__msg__BatteryState(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13394432, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), voltage=12.600000381469727, temperature=25.0, current=-5.0, charge=50.0, capacity=100.0, design_capacity=100.0, percentage=0.5, power_supply_status=2, power_supply_health=1, power_supply_technology=1, present=True, cell_voltage=array([3.7, 3.7, 3.7], dtype=float32), cell_temperature=array([25., 25., 25.], dtype=float32), location='battery_compartment', serial_number='BAT123456', POWER_SUPPLY_STATUS_UNKNOWN=0, POWER_SUPPLY_STATUS_CHARGING=1, POWER_SUPPLY_STATUS_DISCHARGING=2, POWER_SUPPLY_STATUS_NOT_CHARGING=3, POWER_SUPPLY_STATUS_FULL=4, POWER_SUPPLY_HEALTH_UNKNOWN=0, POWER_SUPPLY_HEALTH_GOOD=1, POWER_SUPPLY_HEALTH_OVERHEAT=2, POWER_SUPPLY_HEALTH_DEAD=3, POWER_SUPPLY_HEALTH_OVERVOLTAGE=4, POWER_SUPPLY_HEALTH_UNSPEC_FAILURE=5, POWER_SUPPLY_HEALTH_COLD=6, POWER_SUPPLY_HEALTH_WATCHDOG_TIMER_EXPIRE=7, POWER_SUPPLY_HEALTH_SAFETY_TIMER_EXPIRE=8, POWER_SUPPLY_TECHNOLOGY_UNKNOWN=0, POWER_SUPPLY_TECHNOLOGY_NIMH=1, POWER_SUPPLY_TECHNOLOGY_LION=2, POWER_SUPPLY_TECHNOLOGY_LIPO=3, POWER_SUPPLY_TECHNOLOGY_LIFE=4, POWER_SUPPLY_TECHNOLOGY_NICD=5, POWER_SUPPLY_TECHNOLOGY_LIMN=6, __msgtype__='sensor_msgs/msg/BatteryState')", "msgtype": "sensor_msgs/msg/BatteryState", "raw_data_hex": "00010000fd6e44680062cc000b000000746573745f6672616d6500009a9949410000c8410000a0c0000048420000c8420000c8420000003f0201010103000000cdcc6c40cdcc6c40cdcc6c40030000000000c8410000c8410000c84114000000626174746572795f636f6d706172746d656e74000a00000042415431323334353600", "raw_data_size": 130, "timestamp": 1749315337703302144, "topic": "/test/sensor_msgs/battery_state"}, {"connection_id": 36, "deserialized_data": "sensor_msgs__msg__CameraInfo(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13424384, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), height=480, width=640, distortion_model='plumb_bob', d=array([ 0.1  , -0.2  ,  0.001,  0.002,  0.   ]), k=array([525.,   0., 320.,   0., 525., 240.,   0.,   0.,   1.]), r=array([1., 0., 0., 0., 1., 0., 0., 0., 1.]), p=array([525.,   0., 320.,   0.,   0., 525., 240.,   0.,   0.,   0.,   1.,\n         0.]), binning_x=1, binning_y=1, roi=sensor_msgs__msg__RegionOfInterest(x_offset=0, y_offset=0, height=0, width=0, do_rectify=False, __msgtype__='sensor_msgs/msg/RegionOfInterest'), __msgtype__='sensor_msgs/msg/CameraInfo')", "msgtype": "sensor_msgs/msg/CameraInfo", "raw_data_hex": "00010000fd6e446800d7cc000b000000746573745f6672616d650000e0010000800200000a000000706c756d625f626f6200000005000000000000009a9999999999b93f9a9999999999c9bffca9f1d24d62503ffca9f1d24d62603f0000000000000000000000000068804000000000000000000000000000007440000000000000000000000000006880400000000000006e4000000000000000000000000000000000000000000000f03f000000000000f03f000000000000000000000000000000000000000000000000000000000000f03f000000000000000000000000000000000000000000000000000000000000f03f0000000000688040000000000000000000000000000074400000000000000000000000000000000000000000006880400000000000006e40000000000000000000000000000000000000000000000000000000000000f03f000000000000000001000000010000000000000000000000000000000000000000", "raw_data_size": 365, "timestamp": 1749315337803302144, "topic": "/test/sensor_msgs/camera_info"}, {"connection_id": 37, "deserialized_data": "sensor_msgs__msg__ChannelFloat32(name='intensity', values=array([100., 200., 300.], dtype=float32), __msgtype__='sensor_msgs/msg/ChannelFloat32')", "msgtype": "sensor_msgs/msg/ChannelFloat32", "raw_data_hex": "000100000a000000696e74656e73697479000000030000000000c8420000484300009643", "raw_data_size": 36, "timestamp": 1749315337903302144, "topic": "/test/sensor_msgs/channel_float32"}, {"connection_id": 38, "deserialized_data": "sensor_msgs__msg__CompressedImage(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13469184, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), format='jpeg', data=array([255, 216, 255, 224,   0,  16,  74,  70,  73,  70], dtype=uint8), __msgtype__='sensor_msgs/msg/CompressedImage')", "msgtype": "sensor_msgs/msg/CompressedImage", "raw_data_hex": "00010000fd6e44680086cd000b000000746573745f6672616d650000050000006a706567000000000a000000ffd8ffe000104a464946", "raw_data_size": 54, "timestamp": 1749315338003302144, "topic": "/test/sensor_msgs/compressed_image"}, {"connection_id": 39, "deserialized_data": "sensor_msgs__msg__FluidPressure(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13487872, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), fluid_pressure=101325.0, variance=100.0, __msgtype__='sensor_msgs/msg/FluidPressure')", "msgtype": "sensor_msgs/msg/FluidPressure", "raw_data_hex": "00010000fd6e446800cfcd000b000000746573745f6672616d65000000000000d0bcf8400000000000005940", "raw_data_size": 44, "timestamp": 1749315338103302144, "topic": "/test/sensor_msgs/fluid_pressure"}, {"connection_id": 40, "deserialized_data": "sensor_msgs__msg__Illuminance(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13504000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), illuminance=500.0, variance=10.0, __msgtype__='sensor_msgs/msg/Illuminance')", "msgtype": "sensor_msgs/msg/Illuminance", "raw_data_hex": "00010000fd6e4468000ece000b000000746573745f6672616d6500000000000000407f400000000000002440", "raw_data_size": 44, "timestamp": 1749315338203302144, "topic": "/test/sensor_msgs/illuminance"}, {"connection_id": 41, "deserialized_data": "sensor_msgs__msg__Image(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13522944, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), height=480, width=640, encoding='rgb8', is_bigendian=0, step=1920, data=array([255,   0,   0, 255,   0,   0, 255,   0,   0, 255,   0,   0, 255,\n         0,   0, 255,   0,   0, 255,   0,   0, 255,   0,   0, 255,   0,\n         0, 255,   0,   0], dtype=uint8), __msgtype__='sensor_msgs/msg/Image')", "msgtype": "sensor_msgs/msg/Image", "raw_data_hex": "00010000fd6e44680058ce000b000000746573745f6672616d650000e001000080020000050000007267623800000000800700001e000000ff0000ff0000ff0000ff0000ff0000ff0000ff0000ff0000ff0000ff0000", "raw_data_size": 86, "timestamp": 1749315338303302144, "topic": "/test/sensor_msgs/image"}, {"connection_id": 42, "deserialized_data": "sensor_msgs__msg__Imu(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13542912, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), orientation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), orientation_covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), angular_velocity=geometry_msgs__msg__Vector3(x=0.01, y=0.02, z=0.03, __msgtype__='geometry_msgs/msg/Vector3'), angular_velocity_covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01]), linear_acceleration=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=9.8, __msgtype__='geometry_msgs/msg/Vector3'), linear_acceleration_covariance=array([0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1, 0.1]), __msgtype__='sensor_msgs/msg/Imu')", "msgtype": "sensor_msgs/msg/Imu", "raw_data_hex": "00010000fd6e446800a6ce000b000000746573745f6672616d650000000000000000000000000000000000000000000000000000000000000000f03f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f7b14ae47e17a843f7b14ae47e17a943fb81e85eb51b89e3f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f9a9999999999b93f9a9999999999c93f9a999999999923409a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f9a9999999999b93f", "raw_data_size": 324, "timestamp": 1749315338403302144, "topic": "/test/sensor_msgs/imu"}, {"connection_id": 43, "deserialized_data": "sensor_msgs__msg__JointState(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13567488, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), name=['joint1', 'joint2', 'joint3'], position=array([0.1, 0.2, 0.3]), velocity=array([0.01, 0.02, 0.03]), effort=array([1., 2., 3.]), __msgtype__='sensor_msgs/msg/JointState')", "msgtype": "sensor_msgs/msg/JointState", "raw_data_hex": "00010000fd6e44680006cf000b000000746573745f6672616d65000003000000070000006a6f696e74310000070000006a6f696e74320000070000006a6f696e7433000003000000000000009a9999999999b93f9a9999999999c93f333333333333d33f03000000000000007b14ae47e17a843f7b14ae47e17a943fb81e85eb51b89e3f0300000000000000000000000000f03f00000000000000400000000000000840", "raw_data_size": 164, "timestamp": 1749315338503302144, "topic": "/test/sensor_msgs/joint_state"}, {"connection_id": 44, "deserialized_data": "sensor_msgs__msg__Joy(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13591808, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), axes=array([ 0. ,  0.5, -0.3,  1. ], dtype=float32), buttons=array([0, 1, 0, 1, 0], dtype=int32), __msgtype__='sensor_msgs/msg/Joy')", "msgtype": "sensor_msgs/msg/Joy", "raw_data_hex": "00010000fd6e44680065cf000b000000746573745f6672616d65000004000000000000000000003f9a9999be0000803f050000000000000001000000000000000100000000000000", "raw_data_size": 72, "timestamp": 1749315338603302144, "topic": "/test/sensor_msgs/joy"}, {"connection_id": 45, "deserialized_data": "sensor_msgs__msg__JoyFeedback(type=1, id=0, intensity=0.800000011920929, TYPE_LED=0, TYPE_RUMBLE=1, TYPE_BUZZER=2, __msgtype__='sensor_msgs/msg/JoyFeedback')", "msgtype": "sensor_msgs/msg/JoyFeedback", "raw_data_hex": "0001000001000000cdcc4c3f", "raw_data_size": 12, "timestamp": 1749315338703302144, "topic": "/test/sensor_msgs/joy_feedback"}, {"connection_id": 46, "deserialized_data": "sensor_msgs__msg__JoyFeedbackArray(array=[sensor_msgs__msg__JoyFeedback(type=1, id=0, intensity=0.800000011920929, TYPE_LED=0, TYPE_RUMBLE=1, TYPE_BUZZER=2, __msgtype__='sensor_msgs/msg/JoyFeedback'), sensor_msgs__msg__JoyFeedback(type=2, id=1, intensity=0.5, TYPE_LED=0, TYPE_RUMBLE=1, TYPE_BUZZER=2, __msgtype__='sensor_msgs/msg/JoyFeedback')], __msgtype__='sensor_msgs/msg/JoyFeedbackArray')", "msgtype": "sensor_msgs/msg/JoyFeedbackArray", "raw_data_hex": "000100000200000001000000cdcc4c3f020100000000003f", "raw_data_size": 24, "timestamp": 1749315338803302144, "topic": "/test/sensor_msgs/joy_feedback_array"}, {"connection_id": 47, "deserialized_data": "sensor_msgs__msg__LaserEcho(echoes=array([1.5, 2. , 2.5], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho')", "msgtype": "sensor_msgs/msg/LaserEcho", "raw_data_hex": "00010000030000000000c03f0000004000002040", "raw_data_size": 20, "timestamp": 1749315338903302144, "topic": "/test/sensor_msgs/laser_echo"}, {"connection_id": 48, "deserialized_data": "sensor_msgs__msg__LaserScan(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13669120, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), angle_min=-1.5700000524520874, angle_max=1.5700000524520874, angle_increment=0.009999999776482582, time_increment=0.0, scan_time=0.10000000149011612, range_min=0.10000000149011612, range_max=10.0, ranges=array([1., 2., 3., 4., 5.], dtype=float32), intensities=array([100., 200., 300., 400., 500.], dtype=float32), __msgtype__='sensor_msgs/msg/LaserScan')", "msgtype": "sensor_msgs/msg/LaserScan", "raw_data_hex": "00010000fd6e44680093d0000b000000746573745f6672616d650000c3f5c8bfc3f5c83f0ad7233c00000000cdcccc3dcdcccc3d00002041050000000000803f0000004000004040000080400000a040050000000000c84200004843000096430000c8430000fa43", "raw_data_size": 104, "timestamp": 1749315339003302144, "topic": "/test/sensor_msgs/laser_scan"}, {"connection_id": 49, "deserialized_data": "sensor_msgs__msg__MagneticField(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13690624, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), magnetic_field=geometry_msgs__msg__Vector3(x=0.1, y=0.2, z=0.3, __msgtype__='geometry_msgs/msg/Vector3'), magnetic_field_covariance=array([0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01, 0.01]), __msgtype__='sensor_msgs/msg/MagneticField')", "msgtype": "sensor_msgs/msg/MagneticField", "raw_data_hex": "00010000fd6e446800e7d0000b000000746573745f6672616d6500009a9999999999b93f9a9999999999c93f333333333333d33f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f7b14ae47e17a843f", "raw_data_size": 124, "timestamp": 1749315339103302144, "topic": "/test/sensor_msgs/magnetic_field"}, {"connection_id": 50, "deserialized_data": "sensor_msgs__msg__MultiDOFJointState(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13709824, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), joint_names=['joint1', 'joint2'], transforms=[geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform'), geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=0.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform')], twist=[geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=0.1, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.01, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist'), geometry_msgs__msg__Twist(linear=geometry_msgs__msg__Vector3(x=0.0, y=0.1, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), angular=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.01, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Twist')], wrench=[geometry_msgs__msg__Wrench(force=geometry_msgs__msg__Vector3(x=1.0, y=0.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), torque=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Wrench'), geometry_msgs__msg__Wrench(force=geometry_msgs__msg__Vector3(x=0.0, y=1.0, z=0.0, __msgtype__='geometry_msgs/msg/Vector3'), torque=geometry_msgs__msg__Vector3(x=0.0, y=0.0, z=0.1, __msgtype__='geometry_msgs/msg/Vector3'), __msgtype__='geometry_msgs/msg/Wrench')], __msgtype__='sensor_msgs/msg/MultiDOFJointState')", "msgtype": "sensor_msgs/msg/MultiDOFJointState", "raw_data_hex": "00010000fd6e44680032d1000b000000746573745f6672616d65000002000000070000006a6f696e74310000070000006a6f696e7432000002000000000000000000f03f00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000f03f0000000000000000000000000000f03f0000000000000000000000000000000000000000000000000000000000000000000000000000f03f02000000000000009a9999999999b93f00000000000000000000000000000000000000000000000000000000000000007b14ae47e17a843f00000000000000009a9999999999b93f0000000000000000000000000000000000000000000000007b14ae47e17a843f0200000000000000000000000000f03f00000000000000000000000000000000000000000000000000000000000000009a9999999999b93f0000000000000000000000000000f03f0000000000000000000000000000000000000000000000009a9999999999b93f", "raw_data_size": 380, "timestamp": 1749315339203302144, "topic": "/test/sensor_msgs/multi_dof_joint_state"}, {"connection_id": 51, "deserialized_data": "sensor_msgs__msg__MultiEchoLaserScan(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13744896, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), angle_min=-1.5700000524520874, angle_max=1.5700000524520874, angle_increment=0.10000000149011612, time_increment=0.0010000000474974513, scan_time=0.10000000149011612, range_min=0.10000000149011612, range_max=10.0, ranges=[sensor_msgs__msg__LaserEcho(echoes=array([1.5, 2. ], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho'), sensor_msgs__msg__LaserEcho(echoes=array([2.5, 3. ], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho'), sensor_msgs__msg__LaserEcho(echoes=array([3.5, 4. ], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho')], intensities=[sensor_msgs__msg__LaserEcho(echoes=array([100., 150.], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho'), sensor_msgs__msg__LaserEcho(echoes=array([200., 250.], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho'), sensor_msgs__msg__LaserEcho(echoes=array([300., 350.], dtype=float32), __msgtype__='sensor_msgs/msg/LaserEcho')], __msgtype__='sensor_msgs/msg/MultiEchoLaserScan')", "msgtype": "sensor_msgs/msg/MultiEchoLaserScan", "raw_data_hex": "00010000fd6e446800bbd1000b000000746573745f6672616d650000c3f5c8bfc3f5c83fcdcccc3d6f12833acdcccc3dcdcccc3d0000204103000000020000000000c03f0000004002000000000020400000404002000000000060400000804003000000020000000000c84200001643020000000000484300007a4302000000000096430000af43", "raw_data_size": 136, "timestamp": 1749315339303302144, "topic": "/test/sensor_msgs/multi_echo_laser_scan"}, {"connection_id": 52, "deserialized_data": "sensor_msgs__msg__NavSatFix(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13792000, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), status=sensor_msgs__msg__NavSatStatus(status=0, service=1, STATUS_NO_FIX=-1, STATUS_FIX=0, STATUS_SBAS_FIX=1, STATUS_GBAS_FIX=2, SERVICE_GPS=1, SERVICE_GLONASS=2, SERVICE_COMPASS=4, SERVICE_GALILEO=8, __msgtype__='sensor_msgs/msg/NavSatStatus'), latitude=37.7749, longitude=-122.4194, altitude=100.0, position_covariance=array([1., 1., 1., 1., 1., 1., 1., 1., 1.]), position_covariance_type=1, COVARIANCE_TYPE_UNKNOWN=0, COVARIANCE_TYPE_APPROXIMATED=1, COVARIANCE_TYPE_DIAGONAL_KNOWN=2, COVARIANCE_TYPE_KNOWN=3, __msgtype__='sensor_msgs/msg/NavSatFix')", "msgtype": "sensor_msgs/msg/NavSatFix", "raw_data_hex": "00010000fd6e44680073d2000b000000746573745f6672616d6500000100000000000000d0d556ec2fe3424050fc1873d79a5ec00000000000005940000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f000000000000f03f01", "raw_data_size": 133, "timestamp": 1749315339403302144, "topic": "/test/sensor_msgs/nav_sat_fix"}, {"connection_id": 53, "deserialized_data": "sensor_msgs__msg__NavSatStatus(status=0, service=1, STATUS_NO_FIX=-1, STATUS_FIX=0, STATUS_SBAS_FIX=1, STATUS_GBAS_FIX=2, SERVICE_GPS=1, SERVICE_GLONASS=2, SERVICE_COMPASS=4, SERVICE_GALILEO=8, __msgtype__='sensor_msgs/msg/NavSatStatus')", "msgtype": "sensor_msgs/msg/NavSatStatus", "raw_data_hex": "0001000000000100", "raw_data_size": 8, "timestamp": 1749315339503302144, "topic": "/test/sensor_msgs/nav_sat_status"}, {"connection_id": 54, "deserialized_data": "sensor_msgs__msg__PointCloud(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13830400, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), points=[geometry_msgs__msg__Point32(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=4.0, y=5.0, z=6.0, __msgtype__='geometry_msgs/msg/Point32'), geometry_msgs__msg__Point32(x=7.0, y=8.0, z=9.0, __msgtype__='geometry_msgs/msg/Point32')], channels=[sensor_msgs__msg__ChannelFloat32(name='intensity', values=array([100., 200., 300.], dtype=float32), __msgtype__='sensor_msgs/msg/ChannelFloat32')], __msgtype__='sensor_msgs/msg/PointCloud')", "msgtype": "sensor_msgs/msg/PointCloud", "raw_data_hex": "00010000fd6e44680009d3000b000000746573745f6672616d650000030000000000803f0000004000004040000080400000a0400000c0400000e0400000004100001041010000000a000000696e74656e73697479000000030000000000c8420000484300009643", "raw_data_size": 104, "timestamp": 1749315339603302144, "topic": "/test/sensor_msgs/point_cloud"}, {"connection_id": 55, "deserialized_data": "sensor_msgs__msg__PointCloud2(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13864192, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), height=1, width=3, fields=[sensor_msgs__msg__PointField(name='x', offset=0, datatype=7, count=1, INT8=1, UINT8=2, INT16=3, UINT16=4, INT32=5, UINT32=6, FLOAT32=7, FLOAT64=8, __msgtype__='sensor_msgs/msg/PointField'), sensor_msgs__msg__PointField(name='y', offset=4, datatype=7, count=1, INT8=1, UINT8=2, INT16=3, UINT16=4, INT32=5, UINT32=6, FLOAT32=7, FLOAT64=8, __msgtype__='sensor_msgs/msg/PointField'), sensor_msgs__msg__PointField(name='z', offset=8, datatype=7, count=1, INT8=1, UINT8=2, INT16=3, UINT16=4, INT32=5, UINT32=6, FLOAT32=7, FLOAT64=8, __msgtype__='sensor_msgs/msg/PointField')], is_bigendian=False, point_step=12, row_step=36, data=array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n       0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], dtype=uint8), is_dense=True, __msgtype__='sensor_msgs/msg/PointCloud2')", "msgtype": "sensor_msgs/msg/PointCloud2", "raw_data_hex": "00010000fd6e4468008dd3000b000000746573745f6672616d65000001000000030000000300000002000000780000000000000007000000010000000200000079000000040000000700000001000000020000007a000000080000000700000001000000000000000c000000240000002400000000000000000000000000000000000000000000000000000000000000000000000000000001", "raw_data_size": 153, "timestamp": 1749315339703302144, "topic": "/test/sensor_msgs/point_cloud2"}, {"connection_id": 56, "deserialized_data": "sensor_msgs__msg__PointField(name='x', offset=0, datatype=7, count=1, INT8=1, UINT8=2, INT16=3, UINT16=4, INT32=5, UINT32=6, FLOAT32=7, FLOAT64=8, __msgtype__='sensor_msgs/msg/PointField')", "msgtype": "sensor_msgs/msg/PointField", "raw_data_hex": "000100000200000078000000000000000700000001000000", "raw_data_size": 24, "timestamp": 1749315339803302144, "topic": "/test/sensor_msgs/point_field"}, {"connection_id": 57, "deserialized_data": "sensor_msgs__msg__Range(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13909504, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), radiation_type=0, field_of_view=0.10000000149011612, min_range=0.019999999552965164, max_range=2.0, range=1.5, ULTRASOUND=0, INFRARED=1, __msgtype__='sensor_msgs/msg/Range')", "msgtype": "sensor_msgs/msg/Range", "raw_data_hex": "00010000fd6e4468003ed4000b000000746573745f6672616d650000cdcccc3d0ad7a33c000000400000c03f", "raw_data_size": 44, "timestamp": 1749315339903302144, "topic": "/test/sensor_msgs/range"}, {"connection_id": 58, "deserialized_data": "sensor_msgs__msg__RegionOfInterest(x_offset=10, y_offset=20, height=100, width=200, do_rectify=False, __msgtype__='sensor_msgs/msg/RegionOfInterest')", "msgtype": "sensor_msgs/msg/RegionOfInterest", "raw_data_hex": "000100000a0000001400000064000000c800000000", "raw_data_size": 21, "timestamp": 1749315340003302144, "topic": "/test/sensor_msgs/region_of_interest"}, {"connection_id": 59, "deserialized_data": "sensor_msgs__msg__RelativeHumidity(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13939968, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), relative_humidity=0.65, variance=0.01, __msgtype__='sensor_msgs/msg/RelativeHumidity')", "msgtype": "sensor_msgs/msg/RelativeHumidity", "raw_data_hex": "00010000fd6e446800b5d4000b000000746573745f6672616d650000cdcccccccccce43f7b14ae47e17a843f", "raw_data_size": 44, "timestamp": 1749315340103302144, "topic": "/test/sensor_msgs/relative_humidity"}, {"connection_id": 60, "deserialized_data": "sensor_msgs__msg__Temperature(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13956096, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), temperature=25.5, variance=0.1, __msgtype__='sensor_msgs/msg/Temperature')", "msgtype": "sensor_msgs/msg/Temperature", "raw_data_hex": "00010000fd6e446800f4d4000b000000746573745f6672616d65000000000000008039409a9999999999b93f", "raw_data_size": 44, "timestamp": 1749315340203302144, "topic": "/test/sensor_msgs/temperature"}, {"connection_id": 61, "deserialized_data": "sensor_msgs__msg__TimeReference(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=13971712, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), time_ref=builtin_interfaces__msg__Time(sec=1234567890, nanosec=123456789, __msgtype__='builtin_interfaces/msg/Time'), source='gps', __msgtype__='sensor_msgs/msg/TimeReference')", "msgtype": "sensor_msgs/msg/TimeReference", "raw_data_hex": "00010000fd6e44680031d5000b000000746573745f6672616d650000d202964915cd5b070400000067707300", "raw_data_size": 44, "timestamp": 1749315340303302144, "topic": "/test/sensor_msgs/time_reference"}, {"connection_id": 62, "deserialized_data": "std_msgs__msg__Bool(data=True, __msgtype__='std_msgs/msg/Bool')", "msgtype": "std_msgs/msg/Bool", "raw_data_hex": "0001000001", "raw_data_size": 5, "timestamp": 1749315340403302144, "topic": "/test/std_msgs/bool"}, {"connection_id": 63, "deserialized_data": "std_msgs__msg__Byte(data=42, __msgtype__='std_msgs/msg/Byte')", "msgtype": "std_msgs/msg/Byte", "raw_data_hex": "000100002a", "raw_data_size": 5, "timestamp": 1749315340503302144, "topic": "/test/std_msgs/byte"}, {"connection_id": 64, "deserialized_data": "std_msgs__msg__ByteMultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([65, 66, 67], dtype=int8), __msgtype__='std_msgs/msg/ByteMultiArray')", "msgtype": "std_msgs/msg/ByteMultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000414243", "raw_data_size": 35, "timestamp": 1749315340603302144, "topic": "/test/std_msgs/byte_multi_array"}, {"connection_id": 65, "deserialized_data": "std_msgs__msg__Char(data=65, __msgtype__='std_msgs/msg/Char')", "msgtype": "std_msgs/msg/Char", "raw_data_hex": "0001000041", "raw_data_size": 5, "timestamp": 1749315340703302144, "topic": "/test/std_msgs/char"}, {"connection_id": 66, "deserialized_data": "std_msgs__msg__ColorRGBA(r=1.0, g=0.5, b=0.0, a=0.800000011920929, __msgtype__='std_msgs/msg/ColorRGBA')", "msgtype": "std_msgs/msg/ColorRGBA", "raw_data_hex": "000100000000803f0000003f00000000cdcc4c3f", "raw_data_size": 20, "timestamp": 1749315340803302144, "topic": "/test/std_msgs/color_rgba"}, {"connection_id": 67, "deserialized_data": "std_msgs__msg__Empty(structure_needs_at_least_one_member=0, __msgtype__='std_msgs/msg/Empty')", "msgtype": "std_msgs/msg/Empty", "raw_data_hex": "0001000000", "raw_data_size": 5, "timestamp": 1749315340903302144, "topic": "/test/std_msgs/empty"}, {"connection_id": 68, "deserialized_data": "std_msgs__msg__Float32(data=3.141590118408203, __msgtype__='std_msgs/msg/Float32')", "msgtype": "std_msgs/msg/Float32", "raw_data_hex": "00010000d00f4940", "raw_data_size": 8, "timestamp": 1749315341003302144, "topic": "/test/std_msgs/float32"}, {"connection_id": 69, "deserialized_data": "std_msgs__msg__Float32MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1.1, 2.2, 3.3], dtype=float32), __msgtype__='std_msgs/msg/Float32MultiArray')", "msgtype": "std_msgs/msg/Float32MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000cdcc8c3fcdcc0c4033335340", "raw_data_size": 44, "timestamp": 1749315341103302144, "topic": "/test/std_msgs/float32_multi_array"}, {"connection_id": 70, "deserialized_data": "std_msgs__msg__Float64(data=2.71828, __msgtype__='std_msgs/msg/Float64')", "msgtype": "std_msgs/msg/Float64", "raw_data_hex": "0001000090f7aa9509bf0540", "raw_data_size": 12, "timestamp": 1749315341203302144, "topic": "/test/std_msgs/float64"}, {"connection_id": 71, "deserialized_data": "std_msgs__msg__Float64MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1.11, 2.22, 3.33]), __msgtype__='std_msgs/msg/Float64MultiArray')", "msgtype": "std_msgs/msg/Float64MultiArray", "raw_data_hex": "000100000100000002000000780000000300000003000000000000000300000000000000c3f5285c8fc2f13fc3f5285c8fc20140a4703d0ad7a30a40", "raw_data_size": 60, "timestamp": 1749315341303302144, "topic": "/test/std_msgs/float64_multi_array"}, {"connection_id": 72, "deserialized_data": "std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=14109440, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header')", "msgtype": "std_msgs/msg/Header", "raw_data_hex": "00010000fd6e4468004bd7000b000000746573745f6672616d6500", "raw_data_size": 27, "timestamp": 1749315341403302144, "topic": "/test/std_msgs/header"}, {"connection_id": 73, "deserialized_data": "std_msgs__msg__Int16(data=-1000, __msgtype__='std_msgs/msg/Int16')", "msgtype": "std_msgs/msg/Int16", "raw_data_hex": "0001000018fc", "raw_data_size": 6, "timestamp": 1749315341503302144, "topic": "/test/std_msgs/int16"}, {"connection_id": 74, "deserialized_data": "std_msgs__msg__Int16MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([10, 20, 30], dtype=int16), __msgtype__='std_msgs/msg/Int16MultiArray')", "msgtype": "std_msgs/msg/Int16MultiArray", "raw_data_hex": "00010000010000000200000078000000030000000300000000000000030000000a0014001e00", "raw_data_size": 38, "timestamp": 1749315341603302144, "topic": "/test/std_msgs/int16_multi_array"}, {"connection_id": 75, "deserialized_data": "std_msgs__msg__Int32(data=-100000, __msgtype__='std_msgs/msg/Int32')", "msgtype": "std_msgs/msg/Int32", "raw_data_hex": "000100006079feff", "raw_data_size": 8, "timestamp": 1749315341703302144, "topic": "/test/std_msgs/int32"}, {"connection_id": 76, "deserialized_data": "std_msgs__msg__Int32MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1, 2, 3], dtype=int32), __msgtype__='std_msgs/msg/Int32MultiArray')", "msgtype": "std_msgs/msg/Int32MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000010000000200000003000000", "raw_data_size": 44, "timestamp": 1749315341803302144, "topic": "/test/std_msgs/int32_multi_array"}, {"connection_id": 77, "deserialized_data": "std_msgs__msg__Int64(data=-10000000000, __msgtype__='std_msgs/msg/Int64')", "msgtype": "std_msgs/msg/Int64", "raw_data_hex": "00010000001cf4abfdffffff", "raw_data_size": 12, "timestamp": 1749315341903302144, "topic": "/test/std_msgs/int64"}, {"connection_id": 78, "deserialized_data": "std_msgs__msg__Int64MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([100, 200, 300]), __msgtype__='std_msgs/msg/Int64MultiArray')", "msgtype": "std_msgs/msg/Int64MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000000000006400000000000000c8000000000000002c01000000000000", "raw_data_size": 60, "timestamp": 1749315342003302144, "topic": "/test/std_msgs/int64_multi_array"}, {"connection_id": 79, "deserialized_data": "std_msgs__msg__Int8(data=-42, __msgtype__='std_msgs/msg/Int8')", "msgtype": "std_msgs/msg/Int8", "raw_data_hex": "00010000d6", "raw_data_size": 5, "timestamp": 1749315342103302144, "topic": "/test/std_msgs/int8"}, {"connection_id": 80, "deserialized_data": "std_msgs__msg__Int8MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1, 2, 3], dtype=int8), __msgtype__='std_msgs/msg/Int8MultiArray')", "msgtype": "std_msgs/msg/Int8MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000010203", "raw_data_size": 35, "timestamp": 1749315342203302144, "topic": "/test/std_msgs/int8_multi_array"}, {"connection_id": 81, "deserialized_data": "std_msgs__msg__MultiArrayDimension(label='dimension', size=10, stride=10, __msgtype__='std_msgs/msg/MultiArrayDimension')", "msgtype": "std_msgs/msg/MultiArrayDimension", "raw_data_hex": "000100000a00000064696d656e73696f6e0000000a0000000a000000", "raw_data_size": 28, "timestamp": 1749315342303302144, "topic": "/test/std_msgs/multi_array_dimension"}, {"connection_id": 82, "deserialized_data": "std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout')", "msgtype": "std_msgs/msg/MultiArrayLayout", "raw_data_hex": "00010000010000000200000078000000030000000300000000000000", "raw_data_size": 28, "timestamp": 1749315342403302144, "topic": "/test/std_msgs/multi_array_layout"}, {"connection_id": 83, "deserialized_data": "std_msgs__msg__String(data='Hello, ROS2!', __msgtype__='std_msgs/msg/String')", "msgtype": "std_msgs/msg/String", "raw_data_hex": "000100000d00000048656c6c6f2c20524f53322100", "raw_data_size": 21, "timestamp": 1749315342503302144, "topic": "/test/std_msgs/string"}, {"connection_id": 84, "deserialized_data": "std_msgs__msg__UInt16(data=65535, __msgtype__='std_msgs/msg/UInt16')", "msgtype": "std_msgs/msg/UInt16", "raw_data_hex": "00010000ffff", "raw_data_size": 6, "timestamp": 1749315342603302144, "topic": "/test/std_msgs/uint16"}, {"connection_id": 85, "deserialized_data": "std_msgs__msg__UInt16MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([10, 20, 30], dtype=uint16), __msgtype__='std_msgs/msg/UInt16MultiArray')", "msgtype": "std_msgs/msg/UInt16MultiArray", "raw_data_hex": "00010000010000000200000078000000030000000300000000000000030000000a0014001e00", "raw_data_size": 38, "timestamp": 1749315342703302144, "topic": "/test/std_msgs/uint16_multi_array"}, {"connection_id": 86, "deserialized_data": "std_msgs__msg__UInt32(data=4294967295, __msgtype__='std_msgs/msg/UInt32')", "msgtype": "std_msgs/msg/UInt32", "raw_data_hex": "00010000ffffffff", "raw_data_size": 8, "timestamp": 1749315342803302144, "topic": "/test/std_msgs/uint32"}, {"connection_id": 87, "deserialized_data": "std_msgs__msg__UInt32MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([100, 200, 300], dtype=uint32), __msgtype__='std_msgs/msg/UInt32MultiArray')", "msgtype": "std_msgs/msg/UInt32MultiArray", "raw_data_hex": "000100000100000002000000780000000300000003000000000000000300000064000000c80000002c010000", "raw_data_size": 44, "timestamp": 1749315342903302144, "topic": "/test/std_msgs/uint32_multi_array"}, {"connection_id": 88, "deserialized_data": "std_msgs__msg__UInt64(data=18446744073709551615, __msgtype__='std_msgs/msg/UInt64')", "msgtype": "std_msgs/msg/UInt64", "raw_data_hex": "00010000ffffffffffffffff", "raw_data_size": 12, "timestamp": 1749315343003302144, "topic": "/test/std_msgs/uint64"}, {"connection_id": 89, "deserialized_data": "std_msgs__msg__UInt64MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1000, 2000, 3000], dtype=uint64), __msgtype__='std_msgs/msg/UInt64MultiArray')", "msgtype": "std_msgs/msg/UInt64MultiArray", "raw_data_hex": "000100000100000002000000780000000300000003000000000000000300000000000000e803000000000000d007000000000000b80b000000000000", "raw_data_size": 60, "timestamp": 1749315343103302144, "topic": "/test/std_msgs/uint64_multi_array"}, {"connection_id": 90, "deserialized_data": "std_msgs__msg__UInt8(data=255, __msgtype__='std_msgs/msg/UInt8')", "msgtype": "std_msgs/msg/UInt8", "raw_data_hex": "00010000ff", "raw_data_size": 5, "timestamp": 1749315343203302144, "topic": "/test/std_msgs/uint8"}, {"connection_id": 91, "deserialized_data": "std_msgs__msg__UInt8MultiArray(layout=std_msgs__msg__MultiArrayLayout(dim=[std_msgs__msg__MultiArrayDimension(label='x', size=3, stride=3, __msgtype__='std_msgs/msg/MultiArrayDimension')], data_offset=0, __msgtype__='std_msgs/msg/MultiArrayLayout'), data=array([1, 2, 3], dtype=uint8), __msgtype__='std_msgs/msg/UInt8MultiArray')", "msgtype": "std_msgs/msg/UInt8MultiArray", "raw_data_hex": "0001000001000000020000007800000003000000030000000000000003000000010203", "raw_data_size": 35, "timestamp": 1749315343303302144, "topic": "/test/std_msgs/uint8_multi_array"}, {"connection_id": 92, "deserialized_data": "stereo_msgs__msg__DisparityImage(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=14395392, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), image=sensor_msgs__msg__Image(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=14396416, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), height=480, width=640, encoding='32FC1', is_bigendian=0, step=2560, data=array([  0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,\n        63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,\n         0,   0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,\n         0, 128,  63,   0,   0, 128,  63,   0,   0, 128,  63,   0,   0,\n       128,  63,   0,   0, 128,  63,   0,   0, 128,  63], dtype=uint8), __msgtype__='sensor_msgs/msg/Image'), f=525.0, t=0.10000000149011612, valid_window=sensor_msgs__msg__RegionOfInterest(x_offset=0, y_offset=0, height=480, width=640, do_rectify=False, __msgtype__='sensor_msgs/msg/RegionOfInterest'), min_disparity=0.0, max_disparity=64.0, delta_d=0.125, __msgtype__='stereo_msgs/msg/DisparityImage')", "msgtype": "stereo_msgs/msg/DisparityImage", "raw_data_hex": "00010000fd6e446800a8db000b000000746573745f6672616d650000fd6e446800acdb000b000000746573745f6672616d650000e001000080020000060000003332464331000000000a0000900100000000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f0000803f00400344cdcccc3d0000000000000000e0010000800200000000000000000000000080420000003e", "raw_data_size": 520, "timestamp": 1749315343403302144, "topic": "/test/stereo_msgs/disparity_image"}, {"connection_id": 93, "deserialized_data": "tf2_msgs__msg__TF2Error(error=0, error_string='No error', NO_ERROR=0, LOOKUP_ERROR=1, CONNECTIVITY_ERROR=2, EXTRAPOLATION_ERROR=3, INVALID_ARGUMENT_ERROR=4, TIMEOUT_ERROR=5, TRANSFORM_ERROR=6, __msgtype__='tf2_msgs/msg/TF2Error')", "msgtype": "tf2_msgs/msg/TF2Error", "raw_data_hex": "0001000000000000090000004e6f206572726f7200", "raw_data_size": 21, "timestamp": 1749315343503302144, "topic": "/test/tf2_msgs/tf2_error"}, {"connection_id": 94, "deserialized_data": "tf2_msgs__msg__TFMessage(transforms=[geometry_msgs__msg__TransformStamped(header=std_msgs__msg__Header(stamp=builtin_interfaces__msg__Time(sec=**********, nanosec=14450688, __msgtype__='builtin_interfaces/msg/Time'), frame_id='test_frame', __msgtype__='std_msgs/msg/Header'), child_frame_id='child_frame', transform=geometry_msgs__msg__Transform(translation=geometry_msgs__msg__Vector3(x=1.0, y=2.0, z=3.0, __msgtype__='geometry_msgs/msg/Vector3'), rotation=geometry_msgs__msg__Quaternion(x=0.0, y=0.0, z=0.0, w=1.0, __msgtype__='geometry_msgs/msg/Quaternion'), __msgtype__='geometry_msgs/msg/Transform'), __msgtype__='geometry_msgs/msg/TransformStamped')], __msgtype__='tf2_msgs/msg/TFMessage')", "msgtype": "tf2_msgs/msg/TFMessage", "raw_data_hex": "0001000001000000fd6e44680080dc000b000000746573745f6672616d6500000c0000006368696c645f6672616d650000000000000000000000f03f00000000000000400000000000000840000000000000000000000000000000000000000000000000000000000000f03f", "raw_data_size": 108, "timestamp": 1749315343603302144, "topic": "/test/tf2_msgs/tf_message"}], "metadata": {"compression_format": "", "compression_mode": "", "duration": 18700000001, "end_time": 1749315343603302145, "message_count": 188, "start_time": **********903302144}, "topics": [{"id": 1, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Accel", "serialization_format": "cdr", "topic": "/test/geometry_msgs/accel"}, {"id": 2, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/AccelStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/accel_stamped"}, {"id": 3, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/AccelWithCovariance", "serialization_format": "cdr", "topic": "/test/geometry_msgs/accel_with_covariance"}, {"id": 4, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/AccelWithCovarianceStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/accel_with_covariance_stamped"}, {"id": 5, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Inertia", "serialization_format": "cdr", "topic": "/test/geometry_msgs/inertia"}, {"id": 6, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/InertiaStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/inertia_stamped"}, {"id": 7, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Point", "serialization_format": "cdr", "topic": "/test/geometry_msgs/point"}, {"id": 8, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Point32", "serialization_format": "cdr", "topic": "/test/geometry_msgs/point32"}, {"id": 9, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/PointStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/point_stamped"}, {"id": 10, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Polygon", "serialization_format": "cdr", "topic": "/test/geometry_msgs/polygon"}, {"id": 11, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/PolygonStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/polygon_stamped"}, {"id": 12, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Pose", "serialization_format": "cdr", "topic": "/test/geometry_msgs/pose"}, {"id": 13, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Pose2D", "serialization_format": "cdr", "topic": "/test/geometry_msgs/pose2d"}, {"id": 14, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/PoseArray", "serialization_format": "cdr", "topic": "/test/geometry_msgs/pose_array"}, {"id": 15, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/PoseStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/pose_stamped"}, {"id": 16, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/PoseWithCovariance", "serialization_format": "cdr", "topic": "/test/geometry_msgs/pose_with_covariance"}, {"id": 17, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/PoseWithCovarianceStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/pose_with_covariance_stamped"}, {"id": 18, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Quaternion", "serialization_format": "cdr", "topic": "/test/geometry_msgs/quaternion"}, {"id": 19, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/QuaternionStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/quaternion_stamped"}, {"id": 20, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Transform", "serialization_format": "cdr", "topic": "/test/geometry_msgs/transform"}, {"id": 21, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/TransformStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/transform_stamped"}, {"id": 22, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Twist", "serialization_format": "cdr", "topic": "/test/geometry_msgs/twist"}, {"id": 23, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/TwistStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/twist_stamped"}, {"id": 24, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/TwistWithCovariance", "serialization_format": "cdr", "topic": "/test/geometry_msgs/twist_with_covariance"}, {"id": 25, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/TwistWithCovarianceStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/twist_with_covariance_stamped"}, {"id": 26, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Vector3", "serialization_format": "cdr", "topic": "/test/geometry_msgs/vector3"}, {"id": 27, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Vector3Stamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/vector3_stamped"}, {"id": 28, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/Wrench", "serialization_format": "cdr", "topic": "/test/geometry_msgs/wrench"}, {"id": 29, "md5sum": "", "msgcount": 2, "msgtype": "geometry_msgs/msg/WrenchStamped", "serialization_format": "cdr", "topic": "/test/geometry_msgs/wrench_stamped"}, {"id": 30, "md5sum": "", "msgcount": 2, "msgtype": "nav_msgs/msg/GridCells", "serialization_format": "cdr", "topic": "/test/nav_msgs/grid_cells"}, {"id": 31, "md5sum": "", "msgcount": 2, "msgtype": "nav_msgs/msg/MapMetaData", "serialization_format": "cdr", "topic": "/test/nav_msgs/map_metadata"}, {"id": 32, "md5sum": "", "msgcount": 2, "msgtype": "nav_msgs/msg/OccupancyGrid", "serialization_format": "cdr", "topic": "/test/nav_msgs/occupancy_grid"}, {"id": 33, "md5sum": "", "msgcount": 2, "msgtype": "nav_msgs/msg/Odometry", "serialization_format": "cdr", "topic": "/test/nav_msgs/odometry"}, {"id": 34, "md5sum": "", "msgcount": 2, "msgtype": "nav_msgs/msg/Path", "serialization_format": "cdr", "topic": "/test/nav_msgs/path"}, {"id": 35, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/BatteryState", "serialization_format": "cdr", "topic": "/test/sensor_msgs/battery_state"}, {"id": 36, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/CameraInfo", "serialization_format": "cdr", "topic": "/test/sensor_msgs/camera_info"}, {"id": 37, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/ChannelFloat32", "serialization_format": "cdr", "topic": "/test/sensor_msgs/channel_float32"}, {"id": 38, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/CompressedImage", "serialization_format": "cdr", "topic": "/test/sensor_msgs/compressed_image"}, {"id": 39, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/FluidPressure", "serialization_format": "cdr", "topic": "/test/sensor_msgs/fluid_pressure"}, {"id": 40, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/Illuminance", "serialization_format": "cdr", "topic": "/test/sensor_msgs/illuminance"}, {"id": 41, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/Image", "serialization_format": "cdr", "topic": "/test/sensor_msgs/image"}, {"id": 42, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/Imu", "serialization_format": "cdr", "topic": "/test/sensor_msgs/imu"}, {"id": 43, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/JointState", "serialization_format": "cdr", "topic": "/test/sensor_msgs/joint_state"}, {"id": 44, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/Joy", "serialization_format": "cdr", "topic": "/test/sensor_msgs/joy"}, {"id": 45, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/JoyFeedback", "serialization_format": "cdr", "topic": "/test/sensor_msgs/joy_feedback"}, {"id": 46, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/JoyFeedbackArray", "serialization_format": "cdr", "topic": "/test/sensor_msgs/joy_feedback_array"}, {"id": 47, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/LaserEcho", "serialization_format": "cdr", "topic": "/test/sensor_msgs/laser_echo"}, {"id": 48, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/LaserScan", "serialization_format": "cdr", "topic": "/test/sensor_msgs/laser_scan"}, {"id": 49, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/MagneticField", "serialization_format": "cdr", "topic": "/test/sensor_msgs/magnetic_field"}, {"id": 50, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/MultiDOFJointState", "serialization_format": "cdr", "topic": "/test/sensor_msgs/multi_dof_joint_state"}, {"id": 51, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/MultiEchoLaserScan", "serialization_format": "cdr", "topic": "/test/sensor_msgs/multi_echo_laser_scan"}, {"id": 52, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/NavSatFix", "serialization_format": "cdr", "topic": "/test/sensor_msgs/nav_sat_fix"}, {"id": 53, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/NavSatStatus", "serialization_format": "cdr", "topic": "/test/sensor_msgs/nav_sat_status"}, {"id": 54, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/PointCloud", "serialization_format": "cdr", "topic": "/test/sensor_msgs/point_cloud"}, {"id": 55, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/PointCloud2", "serialization_format": "cdr", "topic": "/test/sensor_msgs/point_cloud2"}, {"id": 56, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/PointField", "serialization_format": "cdr", "topic": "/test/sensor_msgs/point_field"}, {"id": 57, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/Range", "serialization_format": "cdr", "topic": "/test/sensor_msgs/range"}, {"id": 58, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/RegionOfInterest", "serialization_format": "cdr", "topic": "/test/sensor_msgs/region_of_interest"}, {"id": 59, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/RelativeHumidity", "serialization_format": "cdr", "topic": "/test/sensor_msgs/relative_humidity"}, {"id": 60, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/Temperature", "serialization_format": "cdr", "topic": "/test/sensor_msgs/temperature"}, {"id": 61, "md5sum": "", "msgcount": 2, "msgtype": "sensor_msgs/msg/TimeReference", "serialization_format": "cdr", "topic": "/test/sensor_msgs/time_reference"}, {"id": 62, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Bool", "serialization_format": "cdr", "topic": "/test/std_msgs/bool"}, {"id": 63, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Byte", "serialization_format": "cdr", "topic": "/test/std_msgs/byte"}, {"id": 64, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/ByteMultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/byte_multi_array"}, {"id": 65, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Char", "serialization_format": "cdr", "topic": "/test/std_msgs/char"}, {"id": 66, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/ColorRGBA", "serialization_format": "cdr", "topic": "/test/std_msgs/color_rgba"}, {"id": 67, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Empty", "serialization_format": "cdr", "topic": "/test/std_msgs/empty"}, {"id": 68, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Float32", "serialization_format": "cdr", "topic": "/test/std_msgs/float32"}, {"id": 69, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Float32MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/float32_multi_array"}, {"id": 70, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Float64", "serialization_format": "cdr", "topic": "/test/std_msgs/float64"}, {"id": 71, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Float64MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/float64_multi_array"}, {"id": 72, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Header", "serialization_format": "cdr", "topic": "/test/std_msgs/header"}, {"id": 73, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Int16", "serialization_format": "cdr", "topic": "/test/std_msgs/int16"}, {"id": 74, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Int16MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/int16_multi_array"}, {"id": 75, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Int32", "serialization_format": "cdr", "topic": "/test/std_msgs/int32"}, {"id": 76, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Int32MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/int32_multi_array"}, {"id": 77, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Int64", "serialization_format": "cdr", "topic": "/test/std_msgs/int64"}, {"id": 78, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Int64MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/int64_multi_array"}, {"id": 79, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Int8", "serialization_format": "cdr", "topic": "/test/std_msgs/int8"}, {"id": 80, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/Int8MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/int8_multi_array"}, {"id": 81, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/MultiArrayDimension", "serialization_format": "cdr", "topic": "/test/std_msgs/multi_array_dimension"}, {"id": 82, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/MultiArrayLayout", "serialization_format": "cdr", "topic": "/test/std_msgs/multi_array_layout"}, {"id": 83, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/String", "serialization_format": "cdr", "topic": "/test/std_msgs/string"}, {"id": 84, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/UInt16", "serialization_format": "cdr", "topic": "/test/std_msgs/uint16"}, {"id": 85, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/UInt16MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/uint16_multi_array"}, {"id": 86, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/UInt32", "serialization_format": "cdr", "topic": "/test/std_msgs/uint32"}, {"id": 87, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/UInt32MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/uint32_multi_array"}, {"id": 88, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/UInt64", "serialization_format": "cdr", "topic": "/test/std_msgs/uint64"}, {"id": 89, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/UInt64MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/uint64_multi_array"}, {"id": 90, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/UInt8", "serialization_format": "cdr", "topic": "/test/std_msgs/uint8"}, {"id": 91, "md5sum": "", "msgcount": 2, "msgtype": "std_msgs/msg/UInt8MultiArray", "serialization_format": "cdr", "topic": "/test/std_msgs/uint8_multi_array"}, {"id": 92, "md5sum": "", "msgcount": 2, "msgtype": "stereo_msgs/msg/DisparityImage", "serialization_format": "cdr", "topic": "/test/stereo_msgs/disparity_image"}, {"id": 93, "md5sum": "", "msgcount": 2, "msgtype": "tf2_msgs/msg/TF2Error", "serialization_format": "cdr", "topic": "/test/tf2_msgs/tf2_error"}, {"id": 94, "md5sum": "", "msgcount": 2, "msgtype": "tf2_msgs/msg/TFMessage", "serialization_format": "cdr", "topic": "/test/tf2_msgs/tf_message"}]}