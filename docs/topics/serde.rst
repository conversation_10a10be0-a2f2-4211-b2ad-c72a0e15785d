Serde
=====

The :py:mod:`rosbags.serde` package provides a robust serialization and deserialization subsystem, offering code generators tailored for transforming raw message formats. The library supports two primary message formats:

- Common Data Representation (CDR): This serialization format is widely utilized by most ROS2 middlewares. More information on CDR can be found `here <https://www.omg.org/cgi-bin/doc?formal/02-06-51>`__.
- ROS1: This serialization format is specific to the ROS1 stack. Further details on ROS1 serialization can be found `here <http://wiki.ros.org/ROS/Technical%20Overview>`__.

For each supported format, the library incorporates code generators that leverage internal type descriptions generated by :py:mod:`rosbags.typesys`. These code generators compile functions for the following purposes:

- Size Estimation: This functionality facilitates the measurement of the required memory allocation for serializing a Python message object.
- Serialization: It converts Python message objects into raw byte streams, ensuring efficient data transmission.
- Deserialization: This feature converts raw byte streams back into Python message objects, enabling seamless data interpretation.

Moreover, the serde module offers the capability to generate functions for direct conversion between CDR and ROS1 byte streams. This eliminates the need for intermediate deserialization and reserialization, resulting in enhanced efficiency.


Consuming byte streams
----------------------

In every rosbag file format, the size of each raw message is pre-determined. When deserializing raw byte streams, the ``serde`` module operates under the assumption that all message bytes should be fully consumed by the generated deserializer. Any remaining bytes are indicative of potential issues such as incorrect message type or version mismatch, prompting serde to raise an exception.

Notably, serde takes special consideration when handling messages in the Common Data Representation (CDR) format. Some DDS (Data Distribution Service) implementations may append up to three trailing padding bytes to raw messages, and ``serde`` automatically accounts for this by ensuring proper handling of any additional padding bytes.
