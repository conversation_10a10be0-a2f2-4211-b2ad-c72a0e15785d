#!/usr/bin/env python3
"""Debug script to analyze CDR structure of IMU messages."""

import sys
from pathlib import Path
from rosbags.rosbag2 import Reader
from rosbags.serde import deserialize_cdr

def analyze_cdr_structure():
    bag_path = Path("~/Downloads/V1_03_difficult").expanduser()
    
    with Reader(bag_path) as reader:
        target_connections = [x for x in reader.connections if x.topic == '/fcu/imu']
        
        for connection, timestamp, rawdata in reader.messages(target_connections):
            print(f"Message size: {len(rawdata)} bytes")
            print(f"Full hex data:")
            
            # Print hex data in 16-byte chunks
            for i in range(0, len(rawdata), 16):
                chunk = rawdata[i:i+16]
                hex_str = ' '.join(f'{b:02x}' for b in chunk)
                ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
                print(f"{i:04x}: {hex_str:<48} {ascii_str}")
            
            print("\nDeserializing...")
            try:
                msg = deserialize_cdr(rawdata, connection.msgtype)
                print("Success!")
                print(f"Header:")
                print(f"  stamp.sec: {msg.header.stamp.sec}")
                print(f"  stamp.nanosec: {msg.header.stamp.nanosec}")
                print(f"  frame_id: '{msg.header.frame_id}'")
                print(f"Orientation: x={msg.orientation.x:.6f}, y={msg.orientation.y:.6f}, z={msg.orientation.z:.6f}, w={msg.orientation.w:.6f}")
                print(f"Angular velocity: x={msg.angular_velocity.x:.6f}, y={msg.angular_velocity.y:.6f}, z={msg.angular_velocity.z:.6f}")
                print(f"Linear acceleration: x={msg.linear_acceleration.x:.6f}, y={msg.linear_acceleration.y:.6f}, z={msg.linear_acceleration.z:.6f}")
                
                # Print covariance arrays
                print(f"Orientation covariance (first 3): {msg.orientation_covariance[:3]}")
                print(f"Angular velocity covariance (first 3): {msg.angular_velocity_covariance[:3]}")
                print(f"Linear acceleration covariance (first 3): {msg.linear_acceleration_covariance[:3]}")
                
            except Exception as e:
                print(f"Failed: {e}")
            
            break  # Only analyze first message

if __name__ == "__main__":
    analyze_cdr_structure()
