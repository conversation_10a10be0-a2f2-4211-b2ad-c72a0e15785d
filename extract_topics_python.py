#!/usr/bin/env python3
"""
Extract topics from MCAP bag using Python rosbags library for comparison with Rust implementation.
"""

from rosbags.rosbag2 import Reader
from pathlib import Path
import sys

def extract_topic_python(bag_path, topic_name, output_file):
    """Extract a specific topic from the bag file."""
    print(f"Extracting topic '{topic_name}' from bag: {bag_path}")
    print(f"Output file: {output_file}")
    
    try:
        with Reader(bag_path) as reader:
            print(f"\n=== Bag Information ===")
            print(f"Duration: {reader.duration / 1e9:.2f} seconds")
            print(f"Total messages: {reader.message_count}")
            
            # Find the topic
            target_connection = None
            for connection in reader.connections:
                if connection.topic == topic_name:
                    target_connection = connection
                    break
            
            if not target_connection:
                print(f"Error: Topic '{topic_name}' not found in bag")
                return False
            
            print(f"\n=== Topic Information ===")
            print(f"Topic: {target_connection.topic}")
            print(f"  Type: {target_connection.msgtype}")
            print(f"  Messages: {target_connection.msgcount}")
            print(f"  Serialization: cdr")
            
            print(f"\n=== Extracting Messages ===")
            
            # Extract messages
            message_count = 0
            total_data_size = 0
            
            with open(output_file, 'w') as f:
                # Write header based on message type
                if target_connection.msgtype == "sensor_msgs/msg/NavSatFix":
                    f.write("# Format: timestamp_ns,header_sec,header_nanosec,frame_id,status,service,latitude,longitude,altitude,position_covariance_type\n")
                elif target_connection.msgtype == "geometry_msgs/msg/PoseWithCovarianceStamped":
                    f.write("# Format: timestamp_ns,header_sec,header_nanosec,frame_id,position_x,position_y,position_z,orientation_x,orientation_y,orientation_z,orientation_w\n")
                elif target_connection.msgtype == "geometry_msgs/msg/PointStamped":
                    f.write("# Format: timestamp_ns,header_sec,header_nanosec,frame_id,point_x,point_y,point_z\n")
                elif target_connection.msgtype == "tf2_msgs/msg/TFMessage":
                    f.write("# Format: timestamp_ns,transforms_count,data_size,hex_data\n")
                elif target_connection.msgtype == "nav_msgs/msg/Path":
                    f.write("# Format: timestamp_ns,header_sec,header_nanosec,frame_id,poses_count,data_size,hex_data\n")
                elif target_connection.msgtype == "geometry_msgs/msg/PoseArray":
                    f.write("# Format: timestamp_ns,header_sec,header_nanosec,frame_id,poses_count,data_size,hex_data\n")
                else:
                    f.write("# Format: timestamp_ns,data_size,hex_data\n")
                
                # Read messages
                for (connection, timestamp, rawdata) in reader.messages([target_connection]):
                    message_count += 1
                    total_data_size += len(rawdata)
                    
                    if message_count % 1000 == 0:
                        print(f"Processed {message_count} messages...")
                    
                    # For comparison purposes, we'll output the raw data as hex
                    # This matches what the Rust implementation does for unsupported message types
                    hex_data = rawdata.hex()
                    f.write(f"{timestamp},{len(rawdata)},{hex_data}\n")
            
            print(f"\n=== Extraction Complete ===")
            print(f"Messages extracted: {message_count}")
            print(f"Total data size: {total_data_size} bytes ({total_data_size / 1024:.2f} KB)")
            print(f"Output written to: {output_file}")
            
            return True
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    bag_path = Path("../scaled_07_30_00")
    
    topics_to_extract = [
        "/vio/scaled_absolute_pwc",
        "/vio/altitude", 
        "/ros_ap_forwarder/gps",
        "/vio/path",
        "/tf",
        "/vio/keyframe_poses"
    ]
    
    print("Python rosbags topic extraction for comparison")
    print("=" * 60)
    
    for topic in topics_to_extract:
        output_file = f"python_{topic.replace('/', '_').lstrip('_')}.txt"
        print(f"\n{'='*60}")
        success = extract_topic_python(bag_path, topic, output_file)
        if not success:
            print(f"Failed to extract topic: {topic}")

if __name__ == "__main__":
    main()
