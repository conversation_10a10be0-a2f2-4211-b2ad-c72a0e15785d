variables:
  IMAGE: "ghcr.io/astral-sh/uv:python3.13-bookworm"
  UV_CACHE_DIR: $CI_PROJECT_DIR/.uv_cache


stages:
  - test
  - build


test:
  stage: test
  image: $IMAGE
  script:
    - uv run python -m pytest --cov=src --cov-report=term --cov-report=xml --junit-xml=report.xml
    - uv run python -m mypy --no-error-summary docs src tests tools
    - uv run python -m reuse lint
    - uv run python -m ruff check docs src tests tools
    - uv run python -m ruff format --diff docs src tests tools
    - uv run sphinx-build docs public
  coverage: '/\d+\%\s*$/'
  artifacts:
    paths:
      - public
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
      junit: report.xml
  cache:
    key: tools
    paths:
      - .mypy_cache
      - .uv_cache
  parallel:
    matrix:
      - UV_PYTHON:
        - "3.13"
        - "3.12"
        - "3.11"
        - "3.10"


build:
  stage: build
  image: $IMAGE
  script:
    - uv build
  artifacts:
    paths:
      - dist
  cache:
    key: tools
    paths:
      - .mypy_cache
      - .uv_cache


pages:
  stage: build
  image: $IMAGE
  script:
    - ls public
  artifacts:
    paths:
      - public
  only:
    - master
