[build-system]
requires = ["setuptools>=77", "setuptools-scm[toml]"]
build-backend = "setuptools.build_meta"


[project]
name = "rosbags"
authors = [
  { name = "Terna<PERSON>", email = "<EMAIL>" },
]
classifiers = [
  "Development Status :: 4 - Beta",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3 :: Only",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Topic :: Scientific/Engineering",
  "Typing :: Typed",
]
description = "Pure Python library to read, modify, convert, and write rosbag files."
dependencies = [
  "lz4",
  "numpy",
  "ruamel.yaml",
  "typing_extensions >= 4.5",
  "zstandard",
]
dynamic = ["version"]
keywords = [
  "cdr",
  "conversion",
  "deserialization",
  "idl",
  "mcap",
  "message",
  "msg",
  "reader",
  "ros",
  "ros2",
  "rosbag",
  "rosbag2",
  "serialization",
  "writer",
]
license = "Apache-2.0"
readme = "README.rst"
requires-python = ">=3.10"

[project.optional-dependencies]

[project.scripts]
rosbags-convert = "rosbags.convert.cli:main"

[project.urls]
Homepage = "https://gitlab.com/ternaris/rosbags"
Documentation = "https://ternaris.gitlab.io/rosbags"
Source = "https://gitlab.com/ternaris/rosbags"
Issues = "https://gitlab.com/ternaris/rosbags/issues"
Changelog = "https://gitlab.com/ternaris/rosbags/-/blob/master/CHANGES.rst"


[dependency-groups]
dev = [
  "declinate",
  "mypy",
  "pytest",
  "pytest-cov",
  "reuse",
  "ruff",
  "sphinx",
  "sphinx-autodoc-typehints",
  "sphinx-rtd-theme",
]


[tool.coverage]
report.exclude_lines = [
  "pragma: no cover",
  "if TYPE_CHECKING:",
  "if __name__ == '__main__':",
  "@overload",
]
report.show_missing = true
report.skip_covered = true
run.branch = true
run.source = ["src"]


[tool.mypy]
disallow_any_explicit = true
explicit_package_bases = true
mypy_path = "src"
strict = true


[tool.pytest.ini_options]
addopts = ["--verbose"]


[tool.ruff]
line-length = 100
namespace-packages = ["docs", "docs/examples", "tools/bench", "tools/compare"]

[tool.ruff.format]
quote-style = "single"

[tool.ruff.lint]
select = [
  "ALL",
  "D204",
  "D400",
  "D401",
  "D404",
  "D413",
]
ignore = [
  "C901",
  "PLR0911",
  "PLR0912",
  "PLR0913",
  "PLR0915",
  "PLR2004",
  # allow asserts
  "S101",
  # trailing comma
  "COM812",
  # single line string concat
  "ISC001",
]

[tool.ruff.lint.isort]
combine-as-imports = true
force-wrap-aliases = true

[tool.ruff.lint.flake8-builtins]
builtins-allowed-modules = ["types", "typing"]

[tool.ruff.lint.flake8-copyright]
notice-rgx = "(?i)# (C)opyright 2020 - 2025 Ternaris\n# (S)PDX-License-Identifier: Apache-2.0"

[tool.ruff.lint.flake8-quotes]
avoid-escape = false
inline-quotes = "single"

[tool.ruff.lint.pydocstyle]
convention = "google"


[tool.setuptools_scm]
version_scheme = "only-version"
local_scheme = "no-local-version"
